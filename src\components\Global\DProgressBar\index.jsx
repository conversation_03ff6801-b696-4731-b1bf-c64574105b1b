import React from 'react';

import <PERSON>pin<PERSON> from '../DSpinner';
import CheckIcon from '../Icons/CheckIcon';
import FinishLineIcon from '../Icons/FinishLineIcon';

const DProgressBar = ({ steps }) => {
  return (
    <div className="flex flex-col w-full">
      <div className="overflow-x-auto overflow-y-hidden md:overflow-x-visible no-scrollbar py-2">
        <div className="flex items-center w-full min-w-max">
          {steps.map((step, index) => (
            <React.Fragment key={step.id}>
              <div className="flex items-end w-full min-w-[120px] h-10 md:h-7">
                <div className="flex flex-col items-center w-full gap-size1">
                  <div
                    className={`text-xs text-center leading-tight text-black whitespace-nowrap ${
                      step.active
                        ? 'font-medium text-darkGreen-300'
                        : step.completed
                        ? 'font-medium text-darkGreen-300'
                        : 'text-gray-400'
                    }`}
                  >
                    {step.progress_label}
                  </div>
                  {index <= steps.length - 1 && (
                    <div className="w-full h-1 bg-gray-300">
                      {step.completed && (
                        <div className="w-full h-full bg-green-500"></div>
                      )}
                      {step.active && (
                        <div className="w-1/2 h-full bg-green-500"></div>
                      )}
                    </div>
                  )}
                </div>
                <div className="translate-y-[10px]">
                  {step.completed && index !== steps.length - 1 && (
                    <div className="w-7 h-7 bg-darkGreen-10 rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                      <CheckIcon className="w-4 h-4 text-darkGreen-300" />
                    </div>
                  )}
                  {step.active && index !== steps.length - 1 && (
                    <div className="translate-y-[8px]">
                      <DSpinner
                        width={42}
                        height={42}
                        className="text-darkGreen-300 flex-shrink-0"
                      />
                    </div>
                  )}
                  {(step.pending || step.active) &&
                    index === steps.length - 1 && (
                      <div className="w-7 h-7  rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                        <FinishLineIcon className="w-5 h-5 text-black" />
                      </div>
                    )}

                  {step.completed && index === steps.length - 1 && (
                    <div className="w-7 h-7 rounded-full flex items-center justify-center flex-shrink-0 mx-size0">
                      <CheckIcon className="w-4 h-4 text-black" />
                    </div>
                  )}

                  {step.pending && index !== steps.length - 1 && (
                    <div className="translate-y-[8px]">
                      <DSpinner
                        width={42}
                        height={42}
                        className="text-gray-400 flex-shrink-0"
                      />
                    </div>
                  )}
                </div>
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DProgressBar;
