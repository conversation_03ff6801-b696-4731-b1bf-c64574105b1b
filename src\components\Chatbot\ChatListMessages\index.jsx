import generateMarkdownBubble from '@/helpers/generateBubbleMessages';

import DBubbleMessage from '../../Global/DBubbleMessage';
import { DateTime } from 'luxon';
import SourcesPopup from '@/components/SourcesPopup';

const ChatListMessages = ({
  chatContainerRef,
  messages,
  transformLinkUri,
  readonly = false,
  hideFooter = false,
  chatbot_profile_pic,
  isInApp,
  handleFooterButton,
  isDanteFaq,
  showDate = false,
  sources,
  openSources,
  setOpenSources,
  showSources,
  sourcesLoading,
  chatImageLoader,
  place,
  isInHumanHandoverApp,
  interactingWithLiveAgent,
  pollResponse,
  kb_id,
}) => {
  return (
    <div
      ref={chatContainerRef}
      className="flex flex-col w-full overflow-y-auto grow transition duration-300 no-scrollbar"
    >
      {messages?.length > 0 &&
        messages
          .filter((message) => {
            return (
              message.content !== '' ||
              message.status === 'loading' ||
              (message.images && message.images.length > 0)
            );
          })
          .map((message, index) => (
            <DBubbleMessage
              id={message.id}
              id_temp={message.id_temp}
              role={message.role}
              reaction={message.reaction}
              type={message.type}
              status={message.status}
              readonly={readonly}
              hideFooter={hideFooter}
              isDanteFaq={isDanteFaq}
              chatbot_profile_pic={chatbot_profile_pic}
              agent_profile_pic={message.agent_profile_pic}
              agent_name={message.agent_name}
              isInApp={isInApp}
              isInHumanHandoverApp={isInHumanHandoverApp}
              was_answered={message.was_answered}
              pollResponse={pollResponse}
              images={message.images || []}
              handleFooterButton={(action) => {
                if (
                  action === 'play_message' &&
                  message.reaction === 'play_message'
                ) {
                  action = 'stop_message';
                }
                const newAction =
                  message.reaction === action ? 'no_reaction' : action;
                handleFooterButton({
                  message_id: message.id,
                  action: newAction,
                });
              }}
              completed_date={
                showDate &&
                DateTime.fromISO(message.date_created).setLocale('en-US')
              }
              sources={sources}
              openSources={openSources}
              setOpenSources={setOpenSources}
              showSources={showSources}
              sourcesLoading={sourcesLoading}
              chatImageLoader={chatImageLoader}
              place={place}
              interactingWithLiveAgent={interactingWithLiveAgent}
              messageIndex={index}
              kb_id={kb_id}
            >
              {message.status !== 'loading' &&
                message.content !== '' &&
                generateMarkdownBubble(transformLinkUri, message, kb_id)}
            </DBubbleMessage>
          ))}
      {/* {sources?.length > 0 && ( */}
        <SourcesPopup
          open={openSources}
          onClose={() => setOpenSources(false)}
          data={sources}
        />
      {/* )} */}
    </div>
  );
};

export default ChatListMessages;
