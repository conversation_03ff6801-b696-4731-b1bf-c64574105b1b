import React, { useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import Styling from '@/components/Chatbot/Styling';
import DButton from '@/components/Global/DButton';
import useCustomizationData from '@/hooks/useCustomization';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import {
  fakerInitialMessages,
  fakerShortConversation,
  fakerLiveAgentConversation,
} from '@/helpers/stories/generateChatMessages';
import { COMMON_CLASSNAMES, fakerSlots, SYSTEM_FONTS } from '@/constants';
import DAlert from '@/components/Global/DAlert';
import BlurredOverlay from '@/components/BlurredOverlay';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import generateGoogleFonts from '@/helpers/generateGoogleFonts';
import useToast from '@/hooks/useToast';
import ChevronLeftIcon from '@/components/Global/Icons/ChevronLeftIcon';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import ReactRouterPrompt from 'react-router-prompt';
import compareObjects from '@/helpers/compareObjects';
import StyleTag from '@/components/StyleTag';
import {
  saveChatbotImages,
  updateChatbotStyling,
} from '@/services/customization.service';
import { useUserStore } from '@/stores/user/userStore';
import { trackKlaviyoEvent } from '@/services/chatbot.service';

const ChatbotStyling = () => {
  let params = useParams();
  const navigate = useNavigate();
  const { customizationData, setChatbotCustomization, loading } =
    useCustomizationData(true, params.id);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );
  const { updateChatbotCustomization } = useCustomizationStore();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const {user, auth} = useUserStore((state) => state);
  const { addWarningToast, addSuccessToast } = useToast();
  // Temporary state for customization
  const [tempCustomization, setTempCustomization] = useState(customizationData);

  const [activeTheme, setActiveTheme] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [importFontCss, setImportFontCss] = useState('');
  const [isSaveLoading, setIsSaveLoading] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const [changedData, setChangedData] = useState({});
  let formData = new FormData();

  const [currentTab, setCurrentTab] = useState('home');

  const updateCustomizationDataBatch = (data) => {
    setTempCustomization({ ...tempCustomization, ...data });
    setChangedData((prev) => ({ ...prev, ...data }));
  };

  const uploadFile = async (file) => {
    formData.append('file', file);
    const response = await saveChatbotImages(params.id, formData);
    formData.delete('file');
    return response.status === 200 ? response?.data?.url : file;
  };

  const updateStylingData = async () => {
    if (!selectedChatbot?.knowledge_base?.new_design_activated) {
      addWarningToast({
        message:
          'The changes listed below will only take effect once the new design is enabled.',
      });
    }
    setIsSaveLoading(true);
    let chatbotLogo = changedData.embed_logo_url;
    let chatbotIcon = changedData.chatbot_icon;

    if (chatbotLogo instanceof File) {
      chatbotLogo = await uploadFile(chatbotLogo);
    }
    if (chatbotIcon instanceof File) {
      chatbotIcon = await uploadFile(chatbotIcon);
    }

    try {
      const res = await updateChatbotStyling(params.id, {
        ...changedData,
        embed_logo_url: chatbotLogo,
        chatbot_icon: chatbotIcon,
      });
      if (res.status === 200) {
        // Track customized-styling event
        await trackKlaviyoEvent('customized-styling', {
          chatbot_id: params.id,
        });
        addSuccessToast({
          message: 'Styling updated successfully',
        });
        setChatbotCustomization(res.data);
        setTempCustomization(res.data);
        setIsSaveLoading(false);
      }
    } catch (e) {
      setIsSaveLoading(false);
      console.log('error', e);
    }
  };

  useEffect(() => {
    if (!loading && !isSaveLoading) {
      setTempCustomization(customizationData);
    }
  }, [customizationData, loading]);

  useEffect(() => {
    setSidebarOpen(false);
    setIsInPreviewBubblePage(true);
  }, []);

  useEffect(() => {
    if (tempCustomization?.font_name) {
      const fontCss = generateGoogleFonts(tempCustomization?.font_name);
      if (fontCss !== importFontCss) {
        const fontLinkId = `preload-font-${tempCustomization.font_name}`;

        setImportFontCss(fontCss);
        if (
          !document.getElementById(fontLinkId) &&
          !SYSTEM_FONTS.includes(tempCustomization.font_name)
        ) {
          const link = document.createElement('link');
          link.id = fontLinkId;
          link.rel = 'stylesheet';
          link.as = 'style';
          link.href = fontCss;
          document.head.appendChild(link);
        }
      }
    }
  }, [tempCustomization?.font_name]);

  // useEffect(() => {
  //   if (!isAutoScrollEnabled) return;
  //   const interval = setInterval(() => {
  //     setActiveTheme((prev) => (prev + 1) % 9);
  //   }, 3000);
  //   return () => clearInterval(interval);
  // }, [isAutoScrollEnabled]);

  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);

  // Separate style-related customization from functional config
  const functionalConfig = useMemo(() => ({
    kb_id: tempCustomization?.kb_id,
    token: tempCustomization?.token,
    name: tempCustomization?.name,
    kb_name: tempCustomization?.kb_name,
    public: true,
    home_tab_enabled: true,
    main_tabs: tempCustomization?.main_tabs,
    remove_watermark: tempCustomization?.remove_watermark,
    previous_conversation_enabled: tempCustomization?.previous_conversation_enabled,
    show_email_details: tempCustomization?.show_email_details,
    terms_of_use_link: tempCustomization?.terms_of_use_link,
    privacy_policy_link: tempCustomization?.privacy_policy_link,
    initialActiveTab: currentTab,
    onTabChange: (tab) => setCurrentTab(tab),
  }), [
    tempCustomization?.kb_id,
    tempCustomization?.token,
    tempCustomization?.name,
    tempCustomization?.kb_name,
    tempCustomization?.main_tabs,
    tempCustomization?.remove_watermark,
    tempCustomization?.previous_conversation_enabled,
    tempCustomization?.show_email_details,
    tempCustomization?.terms_of_use_link,
    tempCustomization?.privacy_policy_link,
    currentTab,
  ]);

  const styleConfig = useMemo(() => ({
    brand_color: tempCustomization?.brand_color,
    element_color: tempCustomization?.element_color,
    surface_color: tempCustomization?.surface_color,
    alert_color: tempCustomization?.alert_color,
    positive_color: tempCustomization?.positive_color,
    negative_color: tempCustomization?.negative_color,
    font_name: tempCustomization?.font_name,
    font_size: tempCustomization?.font_size,
    embed_logo_url: tempCustomization?.embed_logo_url,
    chatbot_icon: tempCustomization?.chatbot_icon,
    custom_css: tempCustomization?.custom_css,
  }), [
    tempCustomization?.brand_color,
    tempCustomization?.element_color,
    tempCustomization?.surface_color,
    tempCustomization?.alert_color,
    tempCustomization?.positive_color,
    tempCustomization?.negative_color,
    tempCustomization?.font_name,
    tempCustomization?.font_size,
    tempCustomization?.embed_logo_url,
    tempCustomization?.chatbot_icon,
    tempCustomization?.custom_css,
  ]);

  const bubbleConfig = useMemo(() => ({
    ...tempCustomization,
    access_token: auth?.access_token
    // messages:
    //   activeTheme === 2
    //     ? fakerLiveAgentConversation
    //     : activeTheme !== 0 && activeTheme !== 3
    //     ? fakerShortConversation
    //     : undefined,
    // prompt_suggestions:
    //   activeTheme === 1
    //     ? [
    //         {
    //           content: 'Tell me something about Dante AI',
    //           type: 'suggestion',
    //         },
    //         {
    //           content: 'What is the purpose of Dante AI',
    //           type: 'suggestion',
    //         },
    //       ]
    //     : undefined,
    // suggested_prompts_enabled: activeTheme === 1 ? true : false,
    // calendly_integration_enabled: activeTheme === 4 ? true : false,
    // has_mic: activeTheme === 5 ? true : false,
  }), [functionalConfig, styleConfig, activeTheme]);

  const memoizedBubble = useMemo(() => (
    <Bubble
      type="chatbot"
      slots={fakerSlots}
      config={{...bubbleConfig, initialActiveTab: currentTab, remove_watermark: true}}
      isPreviewMode={currentTab === 'home' ? true : false}
      isInApp={true}
    />
  ), [bubbleConfig, currentTab]);

  return (
    <LayoutRightSidebar
      RightSidebar={() => (
        <div className={COMMON_CLASSNAMES.previewBubble}>
          <StyleTag tag=".bubble" tempCustomizationData={styleConfig} />
          {memoizedBubble}
          {/* <div className="flex gap-size1 items-center justify-center">
            <button
              onClick={() => {
                if(activeTheme === 7) {
                  setCurrentTab('home')
                } else if(activeTheme !== 7) {
                  setCurrentTab('chat')
                }
                setActiveTheme((prev) => (prev - 1 + 8) % 8);
              }}
            >
              <ChevronLeftIcon className="text-grey-50" />
            </button>
            {Array.from({ length: 8 }).map((_, index) => (
              <button
                key={index}
                className={`rounded-full ${
                  index === activeTheme
                    ? 'bg-black w-4 h-2'
                    : 'bg-grey-50 size-2'
                }`}
                onClick={() => {
                  setActiveTheme(index);
                  if(index === 0) {
                    setCurrentTab('home')
                  } else if(index !== 0) {
                    setCurrentTab('chat')
                  }
                  setIsAutoScrollEnabled(false);
                }}
              ></button>
            ))}
            <button
              onClick={() => {
                console.log('activeTheme', activeTheme);
                if(activeTheme === 7) {
                  setCurrentTab('home')
                } else if(activeTheme !== 7) {
                  setCurrentTab('chat')
                }
                setActiveTheme((prev) => (prev + 1) % 8);
              }}
            >
              <ChevronRightIcon className="text-grey-50" />
            </button>
          </div> */}
          {/* <DAlert state="alert">
            Use the slider above to preview different screens of your AI
            chatbot.
          </DAlert> */}
        </div>
      )}
    >
      {() => (
        <>
          <LayoutWithButtons
            className="overflow-x-hidden"
            footer={
              <div className="flex items-center justify-between">
                <DButton
                  variant="grey"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  onClick={() => navigate(`/chatbot/${params.id}`)}
                >
                  Cancel
                </DButton>
                <DButton
                  variant="dark"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  loading={isSaveLoading}
                  onClick={updateStylingData}
                  disabled={compareObjects(
                    customizationData,
                    tempCustomization
                  )}
                >
                  Save
                </DButton>
              </div>
            }
          >
            <Styling
              customizationData={tempCustomization}
              updateCustomizationData={(key, value) => {
                setChangedData((prev) => ({ ...prev, [key]: value }));
                setTempCustomization((prev) => ({ ...prev, [key]: value }));
              }}
              updateCustomizationDataBatch={updateCustomizationDataBatch}
              isSaveLoading={isSaveLoading}
            />
          </LayoutWithButtons>
          <ReactRouterPrompt when={unsavedChanges}>
            {({ isActive, onConfirm, onCancel }) => (
              <DConfirmationModal
                open={isActive}
                onClose={onCancel}
                onConfirm={onConfirm}
                title="Are you sure you want to leave this page?"
                description="You have unsaved changes. If you leave, you will lose your changes."
                confirmText="Leave"
                cancelText="Cancel"
                variantConfirm="danger"
              />
            )}
          </ReactRouterPrompt>
        </>
      )}
    </LayoutRightSidebar>
  );
};

export default ChatbotStyling;
