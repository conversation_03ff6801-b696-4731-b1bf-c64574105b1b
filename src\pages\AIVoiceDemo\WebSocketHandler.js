// WebSocket handler for the AI Voice Demo
import { useUserStore } from '@/stores/user/userStore';
import { convertFloat32ToS16PCM, SAMPLE_RATE, NUM_CHANNELS } from './AudioUtils';
import { getVoiceWebSocketUrl, WS_ENDPOINT_TYPES } from '@/services/websocket/voiceWebSocketUtils';

// Class to handle WebSocket connection and audio processing
export class WebSocketHandler {
  constructor({
    onMessage,
    onOpen,
    onClose,
    onError,
    frameRef,
    audioContextRef,
    setUserAudioLevel,
    setSpeaking,
    settings,
    audioConstraints,
    endpointType,
  }) {
    this.wsRef = null;
    this.onMessage = onMessage;
    this.onOpen = onOpen;
    this.onClose = onClose;
    this.onError = onError;
    this.frameRef = frameRef;
    this.audioContextRef = audioContextRef;
    this.setUserAudioLevel = setUserAudioLevel;
    this.setSpeaking = setSpeaking;
    this.settings = settings;
    this.audioConstraints = audioConstraints;
    this.endpointType = endpointType || WS_ENDPOINT_TYPES.DEMO; // Default to DEMO if not specified
    this.sourceRef = null;
    this.scriptProcessorRef = null;
    this.microphoneStreamRef = null;
    this.analyserRef = null;
    this.initSent = false;
  }

  // Initialize WebSocket connection
  initWebSocket() {
    // Close any existing WebSocket connection first
    if (this.wsRef && this.wsRef.readyState !== WebSocket.CLOSED) {
      this.wsRef.close();
    }

    try {
      // Connect to the FastAPI WebSocket endpoint using the specified endpoint type
      const wsUrl = getVoiceWebSocketUrl(this.endpointType);
      this.wsRef = new WebSocket(wsUrl);

      // This is so `event.data` is already an ArrayBuffer
      this.wsRef.binaryType = 'arraybuffer';

      this.wsRef.addEventListener('open', this.handleWebSocketOpen);
      this.wsRef.addEventListener('message', this.handleWebSocketMessage);
      this.wsRef.addEventListener('close', this.handleWebSocketClose);
      this.wsRef.addEventListener('error', this.handleWebSocketError);
      this.initSent = false;
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.onError(error);
    }
  }

  // Send initialization data
  sendInitData() {
    if (!this.wsRef || this.wsRef.readyState !== WebSocket.OPEN) {
      console.error('Cannot send init data: WebSocket not open');
      return;
    }

    if (this.initSent) {
      return;
    }

    try {
      // Determine the appropriate auth token based on the endpoint type
      let authToken;

      if (this.endpointType === WS_ENDPOINT_TYPES.DEMO) {
        // For demo endpoint, always use the demo token
        authToken = 'DanteAIVoiceDemo';
      } else {
        // For preview or browser endpoints, use the JWT token from settings
        // Get user data from the user store if available
        let userToken = null;
        try {
          const userStore = useUserStore.getState();
          userToken = userStore.auth.access_token;
        } catch (e) {
          console.warn('Could not get user token from store:', e);
        }

        // Use the JWT token from user store, or fall back to settings
        authToken = this.settings.authToken || userToken;
      }

      // Use the selectedVoice directly as the voice_id
      // In Edit AI Voice, this will be the database ID (not the external_id)
      const voiceId = this.settings.selectedVoice;

      // Create initialization data payload
      const initData = {
        type: 'dante_init',
        auth_token: authToken,
        kb_id: this.settings.kbId,
        voice_id: voiceId,
        initial_message: this.settings.initialMessage || null,
        conversation_id: this.settings.conversationId || null
      };

      // Add user email if available
      if (this.settings.userEmail) {
        initData.user_email = this.settings.userEmail;
      }

      // Add voice instructions if available (for OpenAI voices)
      if (this.settings.voice_instructions) {
        initData.voice_instructions = this.settings.voice_instructions;
      }

      // Add personality prompt if available
      if (this.settings.personality_prompt) {
        initData.personality_prompt = this.settings.personality_prompt;
      }

      // Send the initialization data
      this.wsRef.send(JSON.stringify(initData));
      this.initSent = true;
    } catch (error) {
      console.error('Error sending initialization data:', error);
    }
  }

  // Handle WebSocket open event
  handleWebSocketOpen = (event) => {
    // Update connection status if handler exists
    if (window.__voicePreviewConnectionHandler && this.endpointType === WS_ENDPOINT_TYPES.PREVIEW) {
      window.__voicePreviewConnectionHandler('connected');
    }

    // Send initialization data first
    this.sendInitData();

    // Configure getUserMedia constraints
    let constraints = { audio: true }; // Default fallback

    // If we have custom audio constraints, use them
    if (this.audioConstraints) {
      if (typeof this.audioConstraints === 'object') {
        // If it's already formatted as constraints, use directly
        constraints = { audio: this.audioConstraints };
      }
    } else {
      // Use default constraints with basic audio quality settings
      constraints = {
        audio: {
          sampleRate: SAMPLE_RATE,
          channelCount: NUM_CHANNELS,
          autoGainControl: true,
          echoCancellation: true,
          noiseSuppression: true,
        }
      };
    }

    // Request microphone access
    navigator.mediaDevices.getUserMedia(constraints).then((stream) => {
      this.microphoneStreamRef = stream;

      // Use original buffer size of 512 for lower latency in sending data
      this.scriptProcessorRef = this.audioContextRef.current.createScriptProcessor(512, 1, 1);
      this.sourceRef = this.audioContextRef.current.createMediaStreamSource(stream);

      // Create analyser for user audio
      this.analyserRef = this.audioContextRef.current.createAnalyser();
      this.analyserRef.fftSize = 256;
      this.analyserRef.smoothingTimeConstant = 0.5; // Add smoother visualization

      this.sourceRef.connect(this.analyserRef);
      this.analyserRef.connect(this.scriptProcessorRef);
      this.scriptProcessorRef.connect(this.audioContextRef.current.destination);

      // Call the onOpen callback
      this.onOpen(this.analyserRef);

      // Set audio process handler
      this.scriptProcessorRef.onaudioprocess = this.handleAudioProcess;
    }).catch((error) => {
      console.error('Error accessing microphone:', error);
      
      // Update connection status for UI
      if (window.__voicePreviewConnectionHandler && this.endpointType === WS_ENDPOINT_TYPES.PREVIEW) {
        window.__voicePreviewConnectionHandler('error', 'Microphone access denied. Please allow microphone access in your browser settings and try again.');
      }
      
      // Close WebSocket connection since we can't proceed without microphone
      if (this.wsRef && this.wsRef.readyState === WebSocket.OPEN) {
        this.wsRef.close(1000, 'Microphone access denied');
      }
      
      // Clean up any existing resources
      this.cleanup();
      
      // Handle specific permission errors
      let errorMessage = 'Could not access microphone';
      if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
        errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No microphone found. Please connect a microphone and try again.';
      } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
        errorMessage = 'Microphone is in use by another application. Please close other applications using the microphone.';
      }
      
      this.onError({
        type: 'microphone',
        error: errorMessage,
        originalError: error,
        disableStopButton: true,
        enableStartButton: true
      });
    });
  }

  // Handle audio processing
  handleAudioProcess = (event) => {
    if (!this.wsRef || !this.initSent || this.wsRef.readyState !== WebSocket.OPEN) {
      return;
    }

    const audioData = event.inputBuffer.getChannelData(0);

    // Calculate RMS value for audio level
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    const rms = Math.sqrt(sum / audioData.length);
    this.setUserAudioLevel(rms);

    // Set speaking state based on audio level
    if (rms > 0.01) {
      this.setSpeaking('user');
    } else if (this.setSpeaking) {
      this.setSpeaking(null);
    }

    // Convert to S16 PCM format and prepare frame for sending
    const pcmS16Array = convertFloat32ToS16PCM(audioData);
    const pcmByteArray = new Uint8Array(pcmS16Array.buffer);

    try {
      const frame = this.frameRef.current.create({
        audio: {
          audio: Array.from(pcmByteArray),
          sampleRate: SAMPLE_RATE,
          numChannels: NUM_CHANNELS
        }
      });

      // Add debugging to ensure data is being properly encoded
      const encodedFrame = new Uint8Array(this.frameRef.current.encode(frame).finish());

      // Send the audio data
      this.wsRef.send(encodedFrame);
    } catch (error) {
      console.error('Error encoding audio frame:', error);
    }
  }

  // Handle WebSocket message event
  handleWebSocketMessage = (event) => {
    this.onMessage(event);
  }

  // Handle WebSocket close event
  handleWebSocketClose = (event) => {
    // Update connection status if handler exists
    if (window.__voicePreviewConnectionHandler && this.endpointType === WS_ENDPOINT_TYPES.PREVIEW) {
      window.__voicePreviewConnectionHandler('disconnected');
    }

    // Provide more detailed information about why the connection closed
    let closeReason = 'Connection closed';
    if (event.code === 1000) {
      closeReason = 'Normal closure';
    } else if (event.code === 1001) {
      closeReason = 'Endpoint going away';
    } else if (event.code === 1002) {
      closeReason = 'Protocol error';
    } else if (event.code === 1003) {
      closeReason = 'Unsupported data';
    } else if (event.code === 1005) {
      closeReason = 'No status received';
    } else if (event.code === 1006) {
      closeReason = 'Abnormal closure - connection to server lost';
    } else if (event.code === 1007) {
      closeReason = 'Invalid frame payload data';
    } else if (event.code === 1008) {
      closeReason = 'Policy violation';
    } else if (event.code === 1009) {
      closeReason = 'Message too big';
    } else if (event.code === 1010) {
      closeReason = 'Extension required';
    } else if (event.code === 1011) {
      closeReason = 'Internal server error';
    } else if (event.code === 1012) {
      closeReason = 'Service restart';
    } else if (event.code === 1013) {
      closeReason = 'Try again later';
    } else if (event.code === 1014) {
      closeReason = 'Bad gateway';
    } else if (event.code === 1015) {
      closeReason = 'TLS handshake failed';
    }

    // Stop calling sound if it's playing - ALWAYS do this regardless of whether close was intentional
    if (window.callingAudioRef && window.callingAudioRef.current) {
      window.callingAudioRef.current.pause();
      window.callingAudioRef.current.currentTime = 0;
    }

    // Clean up any audio processing resources
    this.cleanup();

    // Call the onClose callback with detailed reason
    this.onClose({
      ...event,
      detailedReason: closeReason
    });
  }

  // Handle WebSocket error event
  handleWebSocketError = (event) => {
    // Check if the WebSocket server is available
    const isServerDown = !this.wsRef || this.wsRef.readyState === WebSocket.CLOSED;

    // Update connection status if handler exists
    if (window.__voicePreviewConnectionHandler && this.endpointType === WS_ENDPOINT_TYPES.PREVIEW) {
      window.__voicePreviewConnectionHandler('error', 'WebSocket connection failed. Please try again.');
    }

    // Stop calling sound if it's playing
    if (window.callingAudioRef && window.callingAudioRef.current) {
      window.callingAudioRef.current.pause();
      window.callingAudioRef.current.currentTime = 0;
    }

    // Clean up all resources
    this.cleanup();

    // Call the error handler with detailed information
    this.onError({
      type: 'websocket',
      originalEvent: event,
      serverDown: isServerDown,
      message: 'WebSocket connection failed. Please try again.',
      disableStopButton: true, // Flag to explicitly disable the stop button
      enableStartButton: true  // Flag to explicitly enable the start button
    });
  }

  // Clean up resources when disconnecting
  cleanup() {
    // Stop and disconnect audio processing
    if (this.scriptProcessorRef) {
      try {
        this.scriptProcessorRef.disconnect();
        this.scriptProcessorRef.onaudioprocess = null;
      } catch (e) {
        console.warn('Error disconnecting scriptProcessor:', e);
      }
      this.scriptProcessorRef = null;
    }

    if (this.analyserRef) {
      try {
        this.analyserRef.disconnect();
      } catch (e) {
        console.warn('Error disconnecting analyser:', e);
      }
      this.analyserRef = null;
    }

    if (this.sourceRef) {
      try {
        this.sourceRef.disconnect();
      } catch (e) {
        console.warn('Error disconnecting source:', e);
      }
      this.sourceRef = null;
    }

    // Stop microphone stream tracks
    if (this.microphoneStreamRef) {
      try {
        const tracks = this.microphoneStreamRef.getTracks();
        tracks.forEach(track => track.stop());
      } catch (e) {
        console.warn('Error stopping microphone tracks:', e);
      }
      this.microphoneStreamRef = null;
    }

    // Close WebSocket
    if (this.wsRef) {
      try {
        // Remove event listeners to prevent memory leaks
        this.wsRef.removeEventListener('open', this.handleWebSocketOpen);
        this.wsRef.removeEventListener('message', this.handleWebSocketMessage);
        this.wsRef.removeEventListener('close', this.handleWebSocketClose);
        this.wsRef.removeEventListener('error', this.handleWebSocketError);

        // Only close if not already closed or closing
        if (this.wsRef.readyState !== WebSocket.CLOSED && this.wsRef.readyState !== WebSocket.CLOSING) {
          this.wsRef.close();
        }
      } catch (e) {
        console.warn('Error closing WebSocket:', e);
      }
      this.wsRef = null;
    }

    // Stop calling sound if it's playing (ensures it's stopped in all cleanup scenarios)
    if (window.callingAudioRef && window.callingAudioRef.current) {
      try {
        window.callingAudioRef.current.pause();
        window.callingAudioRef.current.currentTime = 0;
      } catch (e) {
        console.warn('Error stopping calling sound:', e);
      }
    }

    this.initSent = false;
  }
}

// Patch: Use demo websocket endpoint for AI Voice Demo only
class DemoWebSocketHandler extends WebSocketHandler {
  initWebSocket() {
    if (this.wsRef && this.wsRef.readyState !== WebSocket.CLOSED) {
      this.wsRef.close();
    }
    try {
      // Always use the demo endpoint for this handler
      const wsUrl = `${import.meta.env.VITE_APP_AI_VOICE_WEBSOCKET}/browser/demo/ws`;
      this.wsRef = new WebSocket(wsUrl);
      this.wsRef.binaryType = 'arraybuffer';
      this.wsRef.addEventListener('open', this.handleWebSocketOpen);
      this.wsRef.addEventListener('message', this.handleWebSocketMessage);
      this.wsRef.addEventListener('close', this.handleWebSocketClose);
      this.wsRef.addEventListener('error', this.handleWebSocketError);
      this.initSent = false;
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.onError(error);
    }
  }
}

export { DemoWebSocketHandler };
