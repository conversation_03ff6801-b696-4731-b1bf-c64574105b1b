import * as chatbotService from '@/services/chatbot.service';
import DInput from '../Global/DInput/DInput';
import DButton from '../Global/DButton';
import { useState } from 'react';
import useToast from '@/hooks/useToast';

const DChatPassword = ({ kb_id, setCookie, onUnlockSuccess, onBubble }) => {
  const [password, setPassword] = useState('');
  const { addErrorToast, addSuccessToast } = useToast();
  const [errorMessage, setErrorMessage] = useState('');

  const handleUnlock = async () => {
    try {
      const response = await chatbotService.chatbotAuthentication(
        kb_id,
        password
      );
      if (response.status === 200) {
        addSuccessToast({ message: 'AI Chatbot unlocked successfully' });
        setCookie(response.data.session_cookie);
        setPassword('');
        onUnlockSuccess();
      }
    } catch (error) {
      if (error?.response?.data?.detail) {
        console.log('error', error?.response?.data?.detail);
        if (onBubble) {
          setErrorMessage(error?.response?.data?.detail);
        } else {
          addErrorToast({ message: error?.response?.data?.detail });
        }
      }
    }
  };
  return (
    <div className="flex flex-col gap-size2">
      <p className="text-base font-medium tracking-tight">
        AI Chatbot password
      </p>
      <DInput
        placeholder="Enter password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        autoComplete="off"
        type="password"
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleUnlock();
          }
        }}
        error={errorMessage}
      />
      <DButton variant="dark" size="sm" fullWidth onClick={handleUnlock} className='mt-size2'>
        Unlock
      </DButton>
    </div>
  );
};

export default DChatPassword;
