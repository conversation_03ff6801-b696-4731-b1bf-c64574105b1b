import './bubble-embed.css';

(function () {
  let embedOpen = false;
  let styleBaseDivChat = 'position: fixed;width: 0px; height: 0px;max-width: 465px;max-height: 85dvh;min-height: 768px;bottom: 30px;right: 30px;z-index: 999999;opacity: 0;transition: width 0.5s ease, height 0.5s ease;pointer-events: none;border: none;border-radius: 8px;box-shadow: 0px 10px 16px rgba(0, 0, 0, 0.25);display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;';
  let chatIframeStyle = 'position: absolute;top: 0;left: 0;width: 100%;height: 100%;border: none;border-radius: 8px;overflow: auto;';
  let styleBaseTooltip = 'position: fixed;width: 300px;bottom: 90px;right: 30px;z-index: 999990;border: none;pointer-events: auto;background: transparent;';
  let styleDivTooltip = 'position: fixed;width: 300px;height: auto;bottom: 90px;right: 30px;z-index: 999990;cursor: pointer;pointer-events: auto;';
  let styleImgDefault = 'width: 48px; height: 48px; background: #f8f8f8;border-radius: 50%;transition: transform 300ms ease-in-out, box-shadow 1500ms ease-in-out, opacity 500ms ease-in-out;cursor: pointer;box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 10px 0px; z-index: 99998; opacity: 0;';
  let styleImgWrapper = 'position: fixed; bottom: 32px; right: 32px; z-index: 99999; opacity: 0; transition: opacity 0.5s ease-in-out;';
  let greenDotStyle =
    'position: absolute; top: -5px; left: -7px; width: 20px; height: 20px; background: #0DF806; border-radius: 50%; border: 4px solid white; z-index: 99999;';

  const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
  const vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);

  const getConfig = () => {
    const script = document.currentScript;

    const scriptURL = script.src;

    const isConfigInScript = scriptURL.includes('kb_id') && scriptURL.includes('token');

    let urlConfig = '';

    if (isConfigInScript) {
      const url = new URL(scriptURL);
      url.pathname =
        url.searchParams.has('module', 'chatbot') ||
          (url.searchParams.has('kb_id') && url.searchParams.has('token'))
          ? '/embed/bubble/'
          : '/embed/avatar/';
      urlConfig = url.toString();
    } else {
      urlConfig = window.danteEmbed;
    }

    return urlConfig;
  };

  const danteConfig = getConfig();

  const cleanedURL = danteConfig
    ?.replace('chat.helppi-ai.fi', 'app.dante-ai.com')
    ?.replace('chat.challengedevelopgrow.com', 'app.dante-ai.com')
    ?.replace('/embed?', '/embed/?');
  const embedUrl = new URL(cleanedURL);
  const openByDefault = embedUrl.searchParams.get('bubbleopen');
  let cookiesAllowed = false;
  let cookiesEventData = false;
  let promptClickEvent = {};
  let hasSentPrompt = false;

  if (vw < 480) {
    styleImgWrapper += 'bottom: 20px;right: 20px;';
    styleBaseTooltip = 'position: fixed;width: auto;height: auto;bottom: 90px;right: 30px;z-index: 999990;border: none;pointer-events: auto;background: transparent;';
    styleDivTooltip = 'position: fixed;width: calc(100% - 90px);height: auto;bottom: 10px;right: 80px;z-index: 999990;pointer-events: auto;';
    styleBaseDivChat = 'position: fixed;width: 0;height: 0;max-width: 100vw;max-height: 100dvh;bottom: 0;right: 0;left: 0;top: 0;z-index: 999999;opacity: 0;transition: width 0.5s ease, height 0.5s ease;pointer-events: none;border: none;display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;-webkit-overflow-scrolling: touch;';
  } else if (vh < 800) {
    // Adjust for smaller height screens
    styleBaseDivChat = 'position: fixed;width: 0px; height: 0px;max-width: 400px;max-height: 85dvh;min-height: 600px;bottom: 30px;right: 30px;z-index: 999999;opacity: 0;transition: width 0.5s ease, height 0.5s ease;pointer-events: none;border: none;border-radius: 8px;box-shadow: 0px 10px 16px rgba(0, 0, 0, 0.25);display: flex;align-items: center;justify-content: center;background: #f8f8f8;overflow: visible;';
  }

  let styleOpen = styleBaseDivChat + `width: ${vw < 480 ? '100vw' : (vh < 800 ? '400px' : '500px')};height: ${vw < 480 ? '100dvh' : '85dvh'};${vw >= 480 ? `min-height: ${vh < 800 ? '600px' : '768px'};` : ''}opacity: 1;pointer-events: auto;${vw >= 480 ? `max-height: 85dvh; min-height: ${vh < 800 ? '600px' : '768px'};` : ''}`;

  const styleImgHover = styleImgDefault + 'transform: scale(1.10); opacity: 1;';

  const styleImgPulse = styleImgDefault + 'box-shadow: 0px 0px 16px 3px rgba(0, 0, 0, 0.8); opacity: 1;';

  const mountEmbed = () => {
    const toggleEmbed = (forceOpen) => {
      if (forceOpen) {
        embedOpen = true;
      } else {
        // Toggle the state of embedOpen
        embedOpen = !embedOpen;
      }

      // Check if cookies are allowed before saving to localStorage
      if (cookiesAllowed) {
        localStorage.setItem('embed-chatbot-open', embedOpen);
      }

      // Hide the iframe tooltip
      elemIframeTooltip.style.cssText = 'opacity:0;';

      // Set the style of divTooltipIframe to hide it
      divTooltipIframe.style.cssText = styleDivTooltip + 'pointer-events: none;';
      setTimeout(() => {
        // After 500ms, set the style of divTooltipIframe to completely hide it
        divTooltipIframe.style.cssText = 'display:none;';
        divTooltipIframe.remove();
      }, 500);

      // Check if the embed is open
      if (embedOpen) {
        // Asynchronously loading the iframe to prevent unnecessary loading
        // Check if the chatbot iframe is not present, and append it if not
        if (!document.querySelector('#dante_chatbot_iframe')) {
          divChat.appendChild(elemIframe);
        }

        // Set the style of divChat to open
        divChat.style.cssText = styleOpen;

        // If the viewport width is less than 480, prevent scrolling
        if (vw < 480) {
          document.body.style['overflow'] = 'hidden';
        }
      } else {
        // Set the style of divChat to closed
        divChat.style.cssText =
          styleBaseDivChat + 'transition: width 0.5s ease, height 0.5s ease, opacity 0.5s ease';

        if (vw < 480) {
          document.body.style['overflow'] = 'unset';
        }
      }
    };

    const imgHover = () => {
      elemImg.style.cssText = styleImgHover;
    };
    const imgDefault = () => {
      elemImg.style.cssText = styleImgDefault;
    };

    const divTooltipIframe = document.createElement('div');
    const divChat = document.createElement('div');
    const elemIframeTooltip = document.createElement('iframe');
    const elemIframeThirdParty = document.createElement('iframe');

    elemIframeThirdParty.src = `${embedUrl.origin}/thirdparty.html`;
    elemIframeThirdParty.style = 'display:none; opacity:0;';

    const imageWrapper = document.createElement('div');
    imageWrapper.classList.add('dante-image-wrapper');
    imageWrapper.classList.add('animate-fadeIn');
    imageWrapper.style.cssText = styleImgWrapper;

    const greenDot = document.createElement('div');
    greenDot.classList.add('green-dot');
    greenDot.style.cssText = greenDotStyle;

    const urlsWithGreenDot = [
      // 'https://www.dante-ai.com/',
      'https://www.burokoorts.nl/'
    ];

    const currentUrl = window.location.href;
    if (urlsWithGreenDot.some((url) => currentUrl.includes(url))) {
      imageWrapper.appendChild(greenDot);
    }

    const elemImg = document.createElement('img');
    elemImg.alt = 'Chatbot Icon';

    elemImg.style.cssText = styleImgDefault;
    elemImg.addEventListener('click', () => {
      toggleEmbed();
    });
    elemImg.addEventListener('mouseover', imgHover);
    elemImg.addEventListener('mouseout', imgDefault);
    elemImg.classList.add('dante-skeleton');
    elemImg.classList.add('dante-embed-icon');

    const elemIframe = document.createElement('iframe');
    elemIframe.id = 'dante_chatbot_iframe';
    elemIframe.src = cleanedURL;
    elemIframe.title = 'Dante AI Chatbot';
    elemIframe.allow = 'clipboard-write *; microphone *';
    elemIframe.style.cssText = chatIframeStyle;
    elemIframe.setAttribute('scrolling', 'auto');

    divChat.style.cssText = styleBaseDivChat + 'transition: width 0.5s ease, height 0.5s ease;';
    divChat.classList.add('dante-embed-chat');

    document.body.appendChild(divChat);

    fetch(
      `${import.meta.env.VITE_APP_BASE_API}knowledge-bases/customization/shared?kb_id=${embedUrl.searchParams.get('kb_id')}&token=${embedUrl.searchParams.get('token')}`
    )
      .then((response) => response.json())
      .then((data) => {
        console.log('API Response Data:', {
          show_welcome_message_as_tooltip: data?.show_welcome_message_as_tooltip,
          kb_id: embedUrl.searchParams.get('kb_id'),
          viewport_width: vw,
          tooltipShown: localStorage.getItem('tooltipShown'),
          full_data: data
        });

        if (data?.show_welcome_message_as_tooltip) {
          const tooltipShown = localStorage.getItem('tooltipShown') === 'true';

          if (!(embedUrl.searchParams.get('kb_id') === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && vw < 480) && !tooltipShown) {
            // Only create and setup tooltip iframe if show_welcome_message_as_tooltip is true
            const urlTooltip = new URL(
              danteConfig
                .replace('chat.helppi-ai.fi', 'app.dante-ai.com')
                .replace('/embed/tooltips?', '/embed/tooltips/?')
                .replace('/embed/bubble/?', '/embed/tooltips/?')
                .replace('/embed/bubble?', '/embed/tooltips/?')
            );
            if (!['chat.dante-ai.com', 'app.dante-ai.com', 'dante-ai.com'].includes(urlTooltip.hostname)) {
              if (urlTooltip.pathname === '/') {
                urlTooltip.pathname = '/tooltips/';
              }
            }

            elemIframeTooltip.src = urlTooltip.toString();
            elemIframeTooltip.title = 'Dante AI Prompts';
            elemIframeTooltip.style.cssText = styleBaseTooltip;
            elemIframeTooltip.id = 'dante_tooltip_iframe';
            divTooltipIframe.style.cssText = styleDivTooltip;
            divTooltipIframe.classList.add('dante-embed-tooltips');
            divTooltipIframe.appendChild(elemIframeTooltip);

            elemIframeTooltip.addEventListener('click', () => {
              toggleEmbed();
            });

            document.body.appendChild(divTooltipIframe);
          }
        }

        document.body.appendChild(elemIframeThirdParty);

        if (data.chatbot_icon !== undefined && data.chatbot_icon !== null && data.chatbot_icon !== '') {
          elemImg.src = data.chatbot_icon;
          elemImg.classList.remove('dante-skeleton');
        } else {
          elemImg.src = 'https://chat.dante-ai.com/btn-embed.png';
          elemImg.classList.remove('dante-skeleton');
        }

        imageWrapper.appendChild(elemImg);
        document.body.appendChild(imageWrapper);
        
        // Fade in the image wrapper after a short delay
        setTimeout(() => {
          imageWrapper.style.opacity = '1';
          elemImg.style.opacity = '1';
        }, 100);
      })
      .catch(() => { });

    const pulseIn = () => {
      elemImg.style.cssText = styleImgPulse;
      setTimeout(() => pulseOut(), 1500);
    };

    const pulseOut = () => {
      elemImg.style.cssText = styleImgDefault + ' opacity: 1;';
      setTimeout(() => pulseIn(), 1500);
    };

    if (openByDefault === 'true') {
      setTimeout(() => toggleEmbed(), 1000);
    }

    const handleThirdPartyEvent = (event) => {
      if (event.data.eventData === 'allow_third_party_cookie') {
        cookiesAllowed = true;
        if (localStorage.getItem('embed-chatbot-open') === 'true') {
          const lastInteraction = new Date(localStorage.getItem('embed-chatbot-last-interaction'));
          const interactionTime = lastInteraction.getTime() + 5 * 60000;
          const currentTime = new Date().getTime();

          if (interactionTime > currentTime) {
            setTimeout(() => toggleEmbed(true), 1000);
          }
        }
        cookiesEventData = event.data;
      }
    };

    const handleMessages = (event) => {
      let tooltipContainer;
      let tooltipIframe;

      switch (event.data.eventType) {
        case 'siteReady':
          {
            const chatIframe = document.querySelector('#dante_chatbot_iframe');
            chatIframe?.contentWindow.postMessage(cookiesEventData, '*');

            setTimeout(() => {
              if (Object.keys(promptClickEvent).length > 0 && !hasSentPrompt) {
                hasSentPrompt = true;
                chatIframe?.contentWindow.postMessage(promptClickEvent, '*');
                promptClickEvent = {};
              }
            }, 250);
            break;
          }
        case 'chatbotCloseClick':
          toggleEmbed();
          break;

        case 'tooltipCloseClick':
          // Ensure both the container and iframe are removed
          tooltipContainer = document.querySelector('.dante-embed-tooltips');
          tooltipIframe = document.querySelector('#dante_tooltip_iframe');

          if (tooltipContainer) {
            tooltipContainer.style.cssText = 'display:none;';
            tooltipContainer.remove();
          }

          if (tooltipIframe) {
            tooltipIframe.style.cssText = 'display:none;';
            tooltipIframe.remove();
          }
          break;
        case 'promptClick':
          toggleEmbed();

          promptClickEvent = {
            eventType: 'promptClick',
            eventData: event.data.eventData
          };

          setTimeout(() => {
            const chatIframe = document.querySelector('#dante_chatbot_iframe');
            if (chatIframe && chatIframe.contentWindow) {
              chatIframe.contentWindow.postMessage(promptClickEvent, '*');
              promptClickEvent = {};
            }
          }, 500);

          break;
        case 'tooltipClick':
          toggleEmbed();
          break;
        case 'bubbleInteractionDate':
          if (cookiesAllowed) {
            localStorage.setItem('embed-chatbot-last-interaction', event.data.eventData);
          }
          break;
        case 'thirdparty':
          handleThirdPartyEvent(event);
          break;
        case 'tooltipContainerHeight':
          {
            const tooltipIframe = document.querySelector('#dante_tooltip_iframe');
            if (tooltipIframe) {
              tooltipIframe.style.height = event.data.eventData + 'px';
            }
            break;
          }
        default:
      }
    };

    pulseIn();
    window.addEventListener('message', handleMessages);
  };
  const start = (t) => {
    return (
      document.attachEvent ? 'complete' === document.readyState : 'loading' !== document.readyState
    )
      ? t()
      : document.addEventListener('DOMContentLoaded', t);
  };

  start(mountEmbed);
})();
