import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import Bubble from '@/components/Bubble';
import DButton from '@/components/Global/DButton';
import useCustomizationData from '@/hooks/useCustomization';
import useDante<PERSON><PERSON> from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import LayoutWithButtons from '@/layouts/LayoutWithButtons';
import * as chatbotService from '@/services/chatbot.service';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import * as customizationService from '@/services/customization.service';
import { COMMON_CLASSNAMES, fakerSlots, SYSTEM_FONTS } from '@/constants';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import ReactRouterPrompt from 'react-router-prompt';
import compareObjects from '@/helpers/compareObjects';
import StyleTag from '@/components/StyleTag';
import Personality from '@/components/Chatbot/Personality';
import { trackKlaviyoEvent } from '@/services/chatbot.service';

const ChatbotPersonality = () => {
  let params = useParams();
  const { data: personalities } = useDanteApi(
    chatbotService.getPersonalities,
    [],
    {},
    params.id
  );
  const navigate = useNavigate();
  const { auth } = useUserStore((state) => state);
  const { customizationData, setChatbotCustomization, loading } =
    useCustomizationData(true, params.id);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage)

  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);

  const [selectedPersonality, setSelectedPersonality] = useState({
    description: customizationData?.base_system_prompt,
  });

  const { addWarningToast, addSuccessToast } = useToast();
  // Temporary state for customization
  const [tempCustomization, setTempCustomization] = useState(customizationData);

  const [activeTheme, setActiveTheme] = useState(0);
  const [unsavedChanges, setUnsavedChanges] = useState(false);

  const [changedData, setChangedData] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const updateCustomizationDataBatch = (data) => {
    setTempCustomization({ ...tempCustomization, ...data });
  };

  const handleSavePersonality = async () => {
    if (!selectedPersonality || !selectedPersonality.personality_id) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await customizationService.updateChatbotPersonality(params.id, changedData);
      if(response.status === 200) {
        // Track customized-personality event
        const user = useUserStore.getState().user;
        await trackKlaviyoEvent('customized-personality', { 
          chatbot_id: params.id
        });
        setChatbotCustomization(response.data);
        setTempCustomization(response.data);
        addSuccessToast({
          message: 'Personality updated successfully',
        });
      }
    } catch (error) {
      console.error('Failed to save personality:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!loading && !isLoading) {
      setTempCustomization(customizationData);
      setSelectedPersonality({
        personality_id: customizationData?.personalities_template,
        description: customizationData?.base_system_prompt,
      });
    }
  }, [customizationData, loading]);

  useEffect(() => {
    setSidebarOpen(false);
    setIsInPreviewBubblePage(true);
  }, []);


  useEffect(() => {
    if (!tempCustomization || !customizationData) {
      return;
    }

    const hasUnsavedChanges = !compareObjects(
      customizationData,
      tempCustomization
    );

    setUnsavedChanges(hasUnsavedChanges);
  }, [tempCustomization]);


  return (
    <LayoutRightSidebar
      RightSidebar={() => (
          <div className={COMMON_CLASSNAMES.previewBubble}>
            <StyleTag tag=".bubble" tempCustomizationData={tempCustomization} />
            <Bubble
              type="chatbot"
              config={{
                ...tempCustomization,
                access_token: auth?.access_token,
                home_tab_enabled: false,
                remove_watermark: true,
              }}
              initialShouldFetchCustomization={false}
              isInApp={true}
              isPreviewMode={false}
            />
            {/* <div className="flex gap-size1 items-center justify-center">
              <button
                onClick={() => {
                  if (activeTheme === 0) return;
                  setActiveTheme((prev) => (prev - 1 + 9) % 9);
                }}
              >
                <ChevronLeftIcon className="text-grey-50" />
              </button>
              {Array.from({ length: 8 }).map((_, index) => (
                <button
                  key={index}
                  className={`dbutton rounded-full ${
                    index === activeTheme
                      ? 'bg-black w-4 h-2'
                      : 'bg-grey-50 size-2'
                  }`}
                  onClick={() => {
                    setActiveTheme(index);
                    setIsAutoScrollEnabled(false);
                  }}
                ></button>
              ))}
              <button
                onClick={() => {
                  if (activeTheme === 8) return;
                  setActiveTheme((prev) => (prev + 1) % 9);
                }}
              >
                <ChevronRightIcon className="text-grey-50" />
              </button>
            </div> */}
            {/* <DAlert state="alert">
              Use the slider above to preview different screens of your AI
              chatbot.
            </DAlert> */}
          </div>
      )}
    >
      {() => (
        <>
          <LayoutWithButtons
            footer={
              <div className="flex items-center justify-between">
                <DButton
                  variant="grey"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  onClick={() => navigate(`/chatbot/${params.id}`)}
                >
                  Cancel
                </DButton>
                <DButton
                  variant="dark"
                  className="!h-12 w-full md:w-auto md:!min-w-52"
                  loading={isLoading}
                  disabled={compareObjects(
                    customizationData,
                    tempCustomization
                  )}
                  onClick={() => {
                    if (
                      !selectedChatbot?.knowledge_base?.new_design_activated
                    ) {
                      addWarningToast({
                        message:
                          'The changes listed below will only take effect once the new design is enabled.',
                      });
                    }
                    handleSavePersonality();
                  }}
                >
                  Save
                </DButton>
              </div>
            }
          >
            <Personality
                personalities={personalities}
                canEditTemplate={true}
                customizationData={tempCustomization}
                updateCustomizationData={(key, value) => {
                    setTempCustomization((prev) => ({ ...prev, [key]: value }))
                    setChangedData((prev) => ({ ...prev, [key]: value }))
                  }
                }
                setSelectedPersonality={setSelectedPersonality}
                selectedPersonality={selectedPersonality}
            />
          </LayoutWithButtons>
          <ReactRouterPrompt when={unsavedChanges}>
            {({ isActive, onConfirm, onCancel }) => (
              <DConfirmationModal
                open={isActive}
                onClose={onCancel}
                onConfirm={onConfirm}
                title="Are you sure you want to leave this page?"
                description="You have unsaved changes. If you leave, you will lose your changes."
                confirmText="Leave"
                cancelText="Cancel"
                variantConfirm="danger"
              />
            )}
          </ReactRouterPrompt>
        </>
      )}
    </LayoutRightSidebar>
  );
};

export default ChatbotPersonality;
