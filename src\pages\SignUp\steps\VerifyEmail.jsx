import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import * as userService from '@/services/user.service';
import * as authService from '@/services/auth.service';
import useToast from '@/hooks/useToast';
import { useUserStore } from '@/stores/user/userStore';
import { getUserInfo, getUserProfile } from '@/services/user.service';
import { trackSignup, trackTrialSignup } from '@/helpers/analytics';
import EyeIcon from '@/components/Global/Icons/EyeIcon';
import EyeClosedIcon from '@/components/Global/Icons/EyeClosedIcon';

// OTP Input component for verification code
const OTPInput = ({ code, setCode, error, onSubmit }) => {
  const [inputValues, setInputValues] = useState(Array(6).fill(''));
  const inputRefs = Array(6).fill(0).map(() => useState(null)[0]);

  const handleChange = (index, value) => {
    if (value.match(/^[0-9]$/) || value === '') {
      const newInputValues = [...inputValues];
      newInputValues[index] = value;
      setInputValues(newInputValues);

      // Combine digits and update parent component
      setCode(newInputValues.join(''));

      // Auto-focus next input
      if (value !== '' && index < 5) {
        inputRefs[index + 1].focus();
      }
    }
  };

  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace
    if (e.key === 'Backspace') {
      if (inputValues[index] === '' && index > 0) {
        inputRefs[index - 1].focus();
      }
    }

    // Handle Enter key to submit form
    if (e.key === 'Enter' && inputValues.every(val => val !== '')) {
      onSubmit();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').split('').slice(0, 6);

    if (digits.length) {
      const newInputValues = [...inputValues];
      digits.forEach((digit, index) => {
        if (index < 6) {
          newInputValues[index] = digit;
        }
      });

      setInputValues(newInputValues);
      setCode(newInputValues.join(''));

      // Focus the next empty input or the last one
      const nextEmptyIndex = newInputValues.findIndex(val => val === '');
      if (nextEmptyIndex !== -1) {
        inputRefs[nextEmptyIndex].focus();
      } else {
        inputRefs[5].focus();
      }
    }
  };

  return (
    <div>
      <div>
        <div className="flex gap-2 justify-center">
          {inputValues.map((value, index) => (
            <input
              key={index}
              ref={el => inputRefs[index] = el}
              type="text"
              inputMode="numeric"
              maxLength={1}
              value={value}
              onChange={(e) => handleChange(index, e.target.value)}
              onKeyDown={(e) => handleKeyDown(index, e)}
              onPaste={index === 0 ? handlePaste : undefined}
              className={`w-11 h-12 text-center text-xl font-medium rounded-lg border ${
                error ? 'border-red-500' : 'border-gray-200'
              } focus:outline-none focus:ring-2 focus:ring-purple-200 text-min-safe-input`}
            />
          ))}
        </div>
        <div className="h-5 mt-1">
          {error && <p className="text-red-500 text-xs text-center">{error}</p>}
        </div>
      </div>
    </div>
  );
};

const VerifyEmail = ({
  email,
  code,
  setCode,
  goToNextStep,
  goBack,
  loading,
  setLoading,
  error,
  setError,
  contactConsent
}) => {
  const { addSuccessToast } = useToast();
  const [resendCountdown, setResendCountdown] = useState(0);
  const navigate = useNavigate();
  const [urlParams] = useSearchParams();
  const setUser = useUserStore((state) => state.setUser);
  const saveAuthDetail = useUserStore((state) => state.saveAuthDetail);
  const [currentLoadingMessageIndex, setCurrentLoadingMessageIndex] = useState(0);

  // List of loading messages that will be shown in sequence
  const loadingMessages = [
    'Getting things ready...',
    'Creating your account...',
    'Setting up your workspace...',
    'Almost there...'
  ];

  // Update loading message every 2 seconds
  useEffect(() => {
    let interval;
    if (loading) {
      interval = setInterval(() => {
        setCurrentLoadingMessageIndex((prev) =>
          prev < loadingMessages.length - 1 ? prev + 1 : prev
        );
      }, 2000);
    } else {
      setCurrentLoadingMessageIndex(0);
    }
    return () => clearInterval(interval);
  }, [loading]);

  const [codeVerified, setCodeVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

 
  const validateForm = (validationType = 'realtime') => {
    let isValid = true;
    const errors = {};

    
    if (validationType === 'verification' || validationType === 'all') {
      if (!code || code.length !== 6) {
        errors.code = 'Please enter all 6 digits of the verification code';
        isValid = false;
      }
    }
    if (validationType === 'password' || validationType === 'all' || validationType === 'realtime') {
      if (password) { 
        if (password.length < 8) {
          errors.password = 'Password must be at least 8 characters long';
          isValid = false;
        }
        else if (!/[a-z]/i.test(password)) {
          errors.password = 'Password must include at least one letter';
          isValid = false;
        }
        else if (!/[0-9]/.test(password)) {
          errors.password = 'Password must include at least one number';
          isValid = false;
        }
       
        else if (!/[^a-zA-Z0-9]/.test(password)) {
          errors.password = 'Password must include at least one symbol';
          isValid = false;
        }
      } else if (validationType === 'password' || validationType === 'all') {
     
        errors.password = 'Password is required';
        isValid = false;
      }

      if (confirmPassword) {
        if (password !== confirmPassword) {
          errors.confirmPassword = 'Passwords do not match';
          isValid = false;
        }
      } else if (validationType === 'password' || validationType === 'all') {
      
        errors.confirmPassword = 'Please confirm your password';
        isValid = false;
      }
    }

    
    if (validationType === 'verification' || validationType === 'all') {
      setError(errors.code || '');
    }

    if (validationType === 'password' || validationType === 'all' || validationType === 'realtime') {
      setPasswordError(errors.password || '');
      setConfirmPasswordError(errors.confirmPassword || '');
    }

    return isValid;
  };

  
  useEffect(() => {
    validateForm('realtime');
  }, [password, confirmPassword]);

  const handleVerify = async () => {
    if (!validateForm('verification')) return;
    setLoading(true);
    setError('');

    try {
      const response = await userService.verifyEmail(email, code);
      if (response.status === 200) {
        setCodeVerified(true);
        setLoading(false);
      }
    } catch (err) {
      console.error(err);
      setError(
        err.response?.status === 404
          ? 'Invalid verification code'
          : err.response?.data?.detail || 'Error verifying email'
      );
      setLoading(false);
    }
  };

  // Helper function to check if form is ready for submission
  const isFormValid = () => {
    // Use centralized validation to check if password form is valid
    // This is a non-destructive check that doesn't update error states
    return password &&
           confirmPassword &&
           password.length >= 8 &&
           /[a-z]/i.test(password) &&
           /[0-9]/.test(password) &&
           /[^a-zA-Z0-9]/.test(password) &&
           password === confirmPassword &&
           !passwordError &&
           !confirmPasswordError;
  };

  const registerAndLoginUser = async () => {
    if (!validateForm('password')) return;

    setLoading(true);
    try {
      const rewardful_referral = window.Rewardful?.referral;
      const registerResponse = await authService.register({
        email,
        password: password,
        first_name: 'User',
        last_name: '',
        full_name: 'User',
        trial_plan_type: urlParams.get('plan') || 'pro',
        contact_consent: contactConsent,
        kb_id: urlParams.get('kb_id'),
        rewardful_referral,
      });

      if (registerResponse.status === 201) {
        const loginResponse = await authService.login({
          username: email,
          password: password,
          rewardful_referral,
        });

        if (loginResponse.status === 200) {
          const auth = { access_token: loginResponse.data.access_token };

          saveAuthDetail(auth);

          const { data: userInfo } = await getUserInfo(auth.access_token);

          const userProfile = await getUserProfile();

          saveAuthDetail({ ...auth, ...userInfo, user_id: userInfo.id });
          setUser({ ...userInfo, ...userProfile });

          // Track signup event
          trackSignup({
            user_id: userInfo.id,
            email: email,
            first_name: userInfo.first_name || 'User',
            last_name: userInfo.last_name || '',
            tier_name: urlParams.get('plan') || 'pro',
            billing_cycle: 'none',
            signup_method: 'Email'
          });

          navigate('/first-time-setup');
        } else {
          setError('Login failed. Please try again.');
          setLoading(false);
        }
      } else {
        setError('Registration failed. Please check your details.');
        setLoading(false);
      }
    } catch (error) {
      console.error('Error during registration or login:', error);
      setError('An unexpected error occurred. Please try again.');
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendCountdown > 0) return;

    try {
      const response = await userService.resendCode(email);
      if (response.status === 200) {
        addSuccessToast({ message: 'Verification code resent successfully' });

        // Start countdown
        setResendCountdown(52);
        const countdownInterval = setInterval(() => {
          setResendCountdown(prev => {
            if (prev <= 1) {
              clearInterval(countdownInterval);
              return 0;
            }
            return prev - 1;
          });
        }, 1000);
      }
    } catch (err) {
      console.error(err);
      setError('Failed to resend verification code');
    }
  };

  // Email masking function
  const maskEmail = (email) => {
    if (!email) return '';

    const parts = email.split('@');
    if (parts.length !== 2) return email;

    const name = parts[0];
    const domain = parts[1];

    // Show first and last character of the name part
    const maskedName = name.length <= 2
      ? name
      : name.charAt(0) + '*'.repeat(name.length - 2) + name.charAt(name.length - 1);

    return `${maskedName}@${domain}`;
  };

  // Determine email client from domain
  const getEmailClient = (email) => {
    if (!email) return null;

    const domain = email.split('@')[1]?.toLowerCase();

    if (domain?.includes('gmail')) return 'gmail';
    if (domain?.includes('outlook') || domain?.includes('hotmail') || domain?.includes('live')) return 'outlook';

    return null;
  };

  const emailClient = getEmailClient(email);

  const openEmailClient = (client) => {
    let url = '';

    switch (client) {
      case 'gmail':
        url = 'https://mail.google.com';
        break;
      case 'outlook':
        url = 'https://outlook.live.com';
        break;
      default:
        return;
    }

    window.open(url, '_blank');
  };

  return (
    <div className="bg-white rounded-t-xl md:rounded-t-3xl shadow-lg p-4 md:p-7 mx-2 md:mx-0">
      {/* Back Button */}
      <div className="mb-6">
        <button
          onClick={goBack}
          className="flex items-center text-gray-600 font-medium hover:text-gray-900"
          disabled={loading}
        >
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15 10H5M5 10L10 15M5 10L10 5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
          <span className="ml-1">Back</span>
        </button>
      </div>

      {/* Email Icon */}
      <div className="flex justify-center mb-6">
        <div className="relative">
          <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M40 70C56.5685 70 70 56.5685 70 40C70 23.4315 56.5685 10 40 10C23.4315 10 10 23.4315 10 40C10 56.5685 23.4315 70 40 70Z" fill="#F0F0FF"/>
            <path d="M30 35C30 32.791 31.7909 31 34 31H46C48.2091 31 50 32.791 50 35V45C50 47.209 48.2091 49 46 49H34C31.7909 49 30 47.209 30 45V35Z" fill="white" stroke="#8070F2" strokeWidth="2"/>
            <path d="M30 35L40 42L50 35" stroke="#8070F2" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>

          {/* Hearts */}
          <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="absolute top-0 right-0">
            <path d="M10 17.5C10 17.5 17.5 13.75 17.5 8.125C17.5 6.9511 17.0388 5.82419 16.2168 5.00209C15.3947 4.17998 14.2689 3.71977 13.095 3.72C11.5888 3.7225 10.1976 4.45625 10 5.625C9.79861 4.4375 8.41111 3.7225 6.905 3.72C5.73112 3.71977 4.60525 4.17998 3.78315 5.00209C2.96106 5.82419 2.49985 6.9511 2.5 8.125C2.5 13.75 10 17.5 10 17.5Z" fill="#FF6B81"/>
          </svg>

          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" className="absolute top-3 right-5">
            <path d="M8 14C8 14 14 11 14 6.5C14 5.56088 13.6313 4.6597 12.9749 4.00333C12.3185 3.34695 11.4283 2.9765 10.49 2.976C9.27101 2.978 8.15801 3.565 8 4.5C7.83889 3.55 6.73089 2.978 5.51 2.976C4.5717 2.9765 3.68146 3.34695 3.02507 4.00333C2.36869 4.6597 2 5.56088 2 6.5C2 11 8 14 8 14Z" fill="#FF6B81"/>
          </svg>
        </div>
      </div>

      {/* Header */}
      <div className="text-center mb-7 animate-fadeInUpDelayed1">
        <h1 className="text-2xl font-medium mb-2">
          {codeVerified ? 'Create a password' : 'Confirm your email'}
        </h1>
        <p className="text-gray-500 text-sm">
          {codeVerified
            ? 'Create a strong password with a mix of letters, numbers and symbols'
            : `We sent you an email to ${maskEmail(email)} that contains the pin code to sign you in.`}
        </p>
      </div>

      {codeVerified ? (
        // Password Form after code verification
        <div className="mb-6 animate-fadeInUpDelayed2">
          <div className="mb-4">
            <div className="mb-1">
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  placeholder="Password"
                  className={`w-full py-3 px-4 border ${
                    password && passwordError
                      ? 'border-red-500'
                      : 'border-gray-200'
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyDown={(e) =>
                    e.key === 'Enter' && !loading && isFormValid() && registerAndLoginUser()
                  }
                  disabled={loading}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                  onClick={() => setShowPassword(!showPassword)}
                  tabIndex="-1"
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  <span className="flex items-center justify-center w-5 h-5">
                    {showPassword ? <EyeIcon /> : <EyeClosedIcon />}
                  </span>
                </button>
              </div>
              <div className="h-5 mt-1">
                {passwordError && <p className="text-red-500 text-xs">{passwordError}</p>}
              </div>
            </div>

            <div className="mb-1">
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  placeholder="Confirm password"
                  className={`w-full py-3 px-4 border ${
                    confirmPassword && confirmPasswordError
                      ? 'border-red-500'
                      : 'border-gray-200'
                  } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  onKeyDown={(e) =>
                    e.key === 'Enter' && !loading && isFormValid() && registerAndLoginUser()
                  }
                  disabled={loading}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none p-1"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  tabIndex="-1"
                  aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                >
                  <span className="flex items-center justify-center w-5 h-5">
                    {showConfirmPassword ? <EyeIcon /> : <EyeClosedIcon />}
                  </span>
                </button>
              </div>
              <div className="h-5 mt-1">
                {confirmPasswordError && <p className="text-red-500 text-xs">{confirmPasswordError}</p>}
              </div>
            </div>
          </div>

          <div className="animate-fadeInUpDelayed3">
            <button
              onClick={registerAndLoginUser}
              className={`w-full py-3 px-4 rounded-lg font-medium flex items-center justify-center transition ${
                isFormValid() && !loading
                  ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
              disabled={loading || !isFormValid()}
            >
              {loading ? (
                <>
                  <svg className="animate-spin h-5 w-5 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span className="animate-fadeIn">
                    {loadingMessages[currentLoadingMessageIndex]}
                  </span>
                </>
              ) : (
                'Create account'
              )}
            </button>
          </div>
        </div>
      ) : (
        // Verification Code Input
        <>
          <div className="mb-6 animate-fadeInUpDelayed2">
            <OTPInput
              code={code}
              setCode={setCode}
              error={error}
              onSubmit={handleVerify}
            />
          </div>

          {/* Action Button */}
          <div className="mb-6 animate-fadeInUpDelayed3">
            <button
              onClick={handleVerify}
              className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center hover:bg-indigo-700 transition"
              disabled={loading}
            >
              {loading ? (
                <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              ) : null}
              Verify and Continue
            </button>
          </div>

          {/* Resend Link */}
          <div className="text-center mb-6 animate-fadeInUpDelayed3">
            <button
              onClick={handleResendCode}
              className={`text-sm ${resendCountdown > 0 ? 'text-gray-400' : 'text-indigo-600 hover:underline'}`}
              disabled={resendCountdown > 0 || loading}
            >
              {resendCountdown > 0
                ? `Didn't receive the code? Resend code (${resendCountdown}s)`
                : 'Didn\'t receive the code? Resend code'}
            </button>
          </div>

          {/* Email Client Buttons */}
          <div className="flex justify-center gap-4 mb-5 animate-fadeInUpDelayed3">
            {emailClient === 'gmail' || !emailClient ? (
              <button
                onClick={() => openEmailClient('gmail')}
                className="flex items-center px-4 py-2 border border-gray-200 rounded-lg text-sm"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                  <path d="M16.7 10H12.5V13.3H16.7C16.7 13.3 16.7 10 16.7 10Z" fill="#4285F4"/>
                  <path d="M10 16.7C12.6 16.7 13.3 15.8 13.3 15.8L13.3 13.3C13.3 13.3 10 11.7 10 16.7Z" fill="#34A853"/>
                  <path d="M3.3 10C3.3 10 3.3 10 3.3 10C3.3 10 3.3 13.3 3.3 13.3C3.3 13.3 6.7 13.3 6.7 13.3C6.7 13.3 6.7 10 6.7 10C6.7 10 3.3 10 3.3 10Z" fill="#FBBC05"/>
                  <path d="M10 3.3C8.3 3.3 6.7 5 6.7 6.7C6.7 6.7 10 6.7 10 6.7C10 6.7 13.3 6.7 13.3 6.7C13.3 5 11.7 3.3 10 3.3Z" fill="#EA4335"/>
                </svg>
                Open Gmail
              </button>
            ) : null}

            {emailClient === 'outlook' || !emailClient ? (
              <button
                onClick={() => openEmailClient('outlook')}
                className="flex items-center px-4 py-2 border border-gray-200 rounded-lg text-sm"
              >
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" className="mr-2">
                  <path d="M11 3H18V10H11V3Z" fill="#00A4EF"/>
                  <path d="M11 10H18V17H11V10Z" fill="#FFB900"/>
                  <path d="M3 3H10V10H3V3Z" fill="#F25022"/>
                  <path d="M3 10H10V17H3V10Z" fill="#7FBA00"/>
                </svg>
                Open Outlook
              </button>
            ) : null}
          </div>
        </>
      )}

      {/* Wrong Email Link - only show when not verified */}
      {!codeVerified && (
        <div className="text-center animate-fadeInUpDelayed3">
          <button
            onClick={goBack}
            className="text-sm text-indigo-600 hover:underline"
            disabled={loading}
          >
            Wrong email? Try again
          </button>
        </div>
      )}
    </div>
  );
};

export default VerifyEmail;
