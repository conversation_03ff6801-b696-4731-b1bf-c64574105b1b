import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import DButtonIcon from '../Global/DButtonIcon';
import UpRightIcon from '../Global/Icons/UpRightIcon';
import * as tasksService from '@/services/tabs.service';
import { useEffect, useState } from 'react';
import PlayIcon from '../Global/Icons/PlayIcon';
import { usePreviousConversationsStore } from '@/stores/previousConversation/previousConversationStore';
import { DateTime } from 'luxon';
import { useConversationStore } from '@/stores/conversation/conversationStore';
import PoweredByDante from '../PoweredByDante';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import SendIcon from '../Global/Icons/SendIcon';
import DLoading from '../DLoading';

const HomeContent = ({
  kb_id,
  slots,
  isPreviewMode,
  token,
  setActiveTab,
  hidePoweredByDante,
  previous_conversation_enabled = true,
  forceRefetch,
}) => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(false);
  const { conversations, fetchConversations } = usePreviousConversationsStore();
  const { tabsCustomization, updateTabsCustomization } =
    useCustomizationStore();
  const [conversationsFromKbId, setConversationsFromKbId] = useState([]);
  const setCurrentConversation = useConversationStore(
    (state) => state.setCurrentConversation
  );

  const getSlots = async () => {
    setLoading(true);
    try {
      const response = await tasksService.getAllSlots(kb_id, token);
      if (response.status === 200) {
        setData(response.data);
        updateTabsCustomization('home', response.data);
        setLoading(false);
      }
    } catch (e) {
      console.log(e);
    } finally {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    }
  };

  const handleConversationClick = (conversation_id, conversation) => {
    setCurrentConversation({ id: conversation_id, type: 'opening' });
    if (setActiveTab) {
      setActiveTab('chat');
    }
  };

  useEffect(() => {
    const updateData = () => {
      if (kb_id && !isPreviewMode) {
        getSlots();
      } else {
        setData(slots);
      }
    };

    updateData();
  }, [kb_id, slots, forceRefetch]);

  useEffect(() => {
    fetchConversations();
  }, []);

  useEffect(() => {
    if (conversations[kb_id] && !isPreviewMode) {
      setConversationsFromKbId(
        conversations[kb_id]
          .sort((a, b) => b.createdAt - a.createdAt)
          .slice(0, 3)
      );
    } else if (isPreviewMode) {
      setConversationsFromKbId([
        {
          lastMessage: 'How to create an AI agent?',
          createdAt: DateTime.now().minus({ days: 2 }).toMillis(),
        },
        {
          lastMessage: 'How do I white-label my AI Chatbot?',
          createdAt: DateTime.now().minus({ days: 15 }).toMillis(),
        },
      ]);
    }
  }, [conversations]);

  // Combine all arrays (quick_links, link_groups, sliders, meta_links, videos) into one array
  // Make sure to handle empty arrays gracefully
  const sections = [
    ...(data?.link_groups || []),
    ...(data?.quick_links || []),
    ...(data?.sliders || []),
    ...(data?.meta_links || []),
    ...(data?.videos || []),
  ];

  // Sort by order
  sections?.sort((a, b) => (a.order ?? 9999) - (b.order ?? 9999));

  // Helper functions to render each type
  const renderLinkGroup = (link_group) => {
    return (
      <div key={link_group.id} className="link-groups flex flex-col gap-size1">
        <p className="link-groups-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {link_group?.title ?? 'Quick links'}
        </p>
        <div className="link-groups-items flex flex-col gap-size2 mb-size1">
          {link_group?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <a
                key={item.id}
                href={item.url}
                target="_blank"
                rel="noopener noreferrer"
                className="link-groups-item flex items-center justify-between bg-[var(--dt-color-surface-100)] shadow-sm border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 gap-size2"
              >
                <p className="link-groups-item-title text-base font-regular tracking-tight text-[var(--dt-color-element-100)]">
                  {item.name}
                </p>
                <DButtonIcon>
                  <UpRightIcon className="text-[var(--dt-color-element-100)]" />
                </DButtonIcon>
              </a>
            ))}
        </div>
      </div>
    );
  };

  const renderQuickLink = (item) => {
    return (
      <div
        key={item.id}
        className="quick-links flex items-center justify-between border rounded-[10px] border-[var(--dt-color-surface-100)] py-size2 px-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
      >
        <a
          href={item.url}
          target={item?.open_in_new_tab ? '_blank' : '_self'}
          className="quick-links-item text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
        >
          {item.title}
        </a>
      </div>
    );
  };

  const renderSlider = (slider) => {
    return (
      <div className="slider flex flex-col gap-size1" key={slider.id}>
        <p className="slider-title text-lg font-regular tracking-tight text-[var(--dt-color-element-100)] pl-size1">
          {slider?.title ?? 'Updates'}
        </p>
        <div className="slider-items flex gap-size2 w-full overflow-x-auto no-scrollbar whitespace-nowrap">
          {slider?.items
            ?.sort((a, b) => a.order - b.order)
            .map((item) => (
              <div
                key={item.id}
                className="slider-item p-size1 flex-none flex flex-col gap-size2 inline-block w-[248px] bg-[var(--dt-color-surface-100)] border rounded-[10px] border-[var(--dt-color-surface-100)]"
              >
                <img
                  src={item.thumbnail_url}
                  className="slider-item-image h-[120px] object-cover"
                />
                <div className="slider-item-content flex flex-col gap-size0 w-full flex-wrap">
                  <a
                    className="slider-item-title text-sm font-regular text-wrap tracking-tight text-[var(--dt-color-element-75)] max-w-[240px]"
                    href={item.url}
                    target="_blank"
                  >
                    {item.name}
                  </a>
                  <p className="slider-item-description text-xs text-wrap text-[var(--dt-color-element-50)] max-w-[240px] tracking-tight leading-4">
                    {item.description.length > 100
                      ? item.description.substring(0, 150) + '...'
                      : item.description}
                  </p>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  };

  const renderMetaLink = (meta_link) => {
    return (
      <div
        className="meta-link border rounded-[10px] border-[var(--dt-color-surface-100)] p-size2 flex flex-col items-start gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
        key={meta_link.id}
      >
        {meta_link?.image_url && <img src={meta_link?.image_url} />}
        <div className="meta-link-content flex flex-col gap-size0">
          <a
            href={meta_link?.url}
            target={meta_link?.open_in_new_tab ? '_blank' : '_self'}
            className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)]"
          >
            {meta_link?.title}
          </a>
          <p className="text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
            {meta_link?.description}
          </p>
        </div>
      </div>
    );
  };

  const renderVideo = (item) => {
    return (
      <div
        key={item.id}
        className="video p-size1 flex flex-col gap-size2 inline-block w-full max-w-[90%] mx-auto"
      >
        <div className="relative">
          {item.show_thumbnail && <img src={item.thumbnail_url} />}
          <div className="video-play-button absolute inset-0 size-14 bg-white/15 rounded-full backdrop-filter flex items-center justify-center m-auto shadow-sm">
            <button onClick={() => window.open(item.url, '_blank')}>
              <PlayIcon className="text-white size-8" />
            </button>
          </div>
        </div>
        <div className="video-content flex flex-col gap-size0 w-full flex-wrap">
          <p className="video-title text-sm font-regular tracking-tight text-[var(--dt-color-element-75)]">
            {item.title}
          </p>
          <p className="video-description text-xs text-[var(--dt-color-element-50)] tracking-tight text-wrap">
            {item.description}
          </p>
        </div>
      </div>
    );
  };

  // if(loading && (!slots || !isPreviewMode)) {
  //   return <DLoading show={true} />
  // }

  return (
    <div className="flex flex-col gap-size3 mt-[35px] h-[calc(100%-35px)]">
      <span className="welcome-message text-xl font-regular tracking-tight text-[var(--dt-color-element-100)]"></span>
      {kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && sections.filter((section) => section.type === 'quick_link').map((section) => {
        return (
          <div key={section.id}>
            {section.type === 'quick_link' && renderQuickLink(section)}
          </div>
        )
      })}
      <div
        className="send-message-container bg-[var(--dt-color-surface-100)] rounded-[10px] p-size2 flex items-center justify-between cursor-pointer"
        onClick={() => setActiveTab('chat')}
      >
        <p className="send-message-text text-[var(--dt-color-element-100)]">
          Send us a message
        </p>
        <DButtonIcon onClick={() => setActiveTab('chat')}>
          <SendIcon className="text-[var(--dt-color-element-100)] size-4" />
        </DButtonIcon>
      </div>
      {conversationsFromKbId.length > 0 && previous_conversation_enabled && (
        <div className="previous-conversations flex flex-col gap-size2">
          <p className="previous-conversations-text text-lg font-regular tracking-tight text-[var(--dt-color-element-100)]">
            Previous conversations
          </p>
          {conversationsFromKbId.map((conversation, index) => (
            <button
              disabled={isPreviewMode}
              key={index}
              className="flex text-left items-start border rounded-[10px] border-[var(--dt-color-surface-100)] p-size1 gap-size2 bg-[var(--dt-color-surface-100)] shadow-sm"
              onClick={() =>
                handleConversationClick(conversation.id, conversation)
              }
            >
              <div className="previous-conversations-icon w-4">
                <AiChatbotIcon
                  className={'mt-3 text-[var(--dt-color-brand-100)]'}
                />
              </div>
              <div className="previous-conversations-content flex flex-col  w-[calc(100%-40px)]">
                <p className="text-base font-regular tracking-tight text-[var(--dt-color-element-100)] truncate">
                  {conversation.lastMessage
                    ? conversation.lastMessage
                    : `Conversation - ${conversation.id}`}
                </p>
                <p className="previous-conversations-date text-xs font-light tracking-tight text-[var(--dt-color-element-50)]">
                  {DateTime.fromMillis(
                    conversation.createdAt ?? DateTime.now().toMillis(),
                    {
                      zone: 'utc',
                    }
                  ).toRelative()}
                </p>
              </div>
            </button>
          ))}
        </div>
      )}
      {sections.map((section) => {
        if (kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' && section.type === 'quick_link') {
          return null;
        }

        switch (section.type) {
          case 'quick_link':
            return renderQuickLink(section);
          case 'link_group':
            return renderLinkGroup(section);
          case 'slider':
            return renderSlider(section);
          case 'meta_link':
            return renderMetaLink(section);
          case 'video':
            return renderVideo(section);
          default:
            return null;
        }
      })}

      {!hidePoweredByDante && (
        <div className="flex gap-size0 items-center justify-center mt-auto pb-size2">
          <PoweredByDante variant="color" isPreviewMode={isPreviewMode} />
        </div>
      )}
    </div>
  );
};

export default HomeContent;
