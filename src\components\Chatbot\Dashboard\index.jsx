import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { DANTE_ICON, LLM_MODEL_DEFAULT, STATUS } from '@/constants';
import { dataChart } from '@/helpers/stories/generateDateChart';
import { infoChatbot } from '@/helpers/stories/generateListChatbots';
import EmptyDashboard from '@/assets/empty_dashboard.png';
import EmptyDashboardDark from '@/assets/empty_dashboard_dark.png';
import ChatbotBlock from '../ChatbotBlock';
import DanteFAQ from '../DanteFAQ';
import LayoutMain from '@/layouts/LayoutMain';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DButton from '@/components/Global/DButton';
import DModalShareChatbot from '../DModalShareChatbot';
import { Transition } from '@headlessui/react';
import checkEnv from '@/helpers/environmentCheck';
import { useCreateChatbotStore } from '@/stores/chatbot/createChatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import { trackKlaviyoEvent } from '@/services/chatbot.service';
import { trackCreateChatbotClick } from '@/helpers/analytics';

import AddChatbotIcon from '@/components/Global/Icons/AddChatbotIcon';
const ChatbotDashboard = ({ chatbots = [] }) => {
  const navigate = useNavigate();
  const resetChatbotData = useCreateChatbotStore(
    (state) => state.resetChatbotData
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const [showShareChatbot, setShowShareChatbot] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [firstMessage, setFirstMessage] = useState('');
  const [kbId, setKbId] = useState('');
  const [llmModel, setLlmModel] = useState(
    selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value
  );
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const user = useUserStore((state) => state.user);

  const { setUserMode, selectedTeam, setSelectedTeam } = useTeamManagementStore(
    (state) => state
  );

  const handleShareChatbot = ({ kb_id, llm_model }) => {
    setShowShareChatbot(true);
    setKbId(kb_id);
    setLlmModel(llm_model);
  };

  useEffect(() => {
    setSidebarOpen(true);
  }, []);

  const handleImageLoad = () => {
    setIsImageLoaded(true);
  };

  return (
    <LayoutMain
      title={chatbots.length > 0 && !showChat ? 'Manage AI Chatbots' : ''}
      variant="transparent-minimal"
      boxMode={false}
    >
      <div className="absolute w-full h-full flex flex-col overflow-hidden">
        {chatbots.length > 0 ? (
          <>
            <Transition show={!showChat}>
              <div className="transition-all duration-200 data-[closed]:opacity-0 data-[enter]:delay-200 flex flex-1 min-h-0 h-full">
                <div className="flex-1 overflow-y-auto grow h-full scrollbar">
                  <div className="dashboard-grid_content grid gap-size5">
                    {(!selectedTeam ||
                      (selectedTeam &&
                        checkTeamManagementPermission('create_chatbot'))) && (
                      <div className="dashboard-create_btn bg-white flex gap-size1 md:gap-0 md:block  rounded-size1">
                        <button
                          className={'dbutton flex flex-col items-center rounded-size1 py-size2 px-size3 md:p-size3 justify-center text-grey-20 w-full min-h-[270px]'}
                          onClick={() => {
                            if (checkEnv()) {
                              trackCreateChatbotClick({ user_id: user?.email });
                            }
                            resetChatbotData();
                            navigate('/chatbot/create');
                          }}
                        >
                          <AddChatbotIcon className="size-10" />
                          <span className="ml-2 mt-4 text-grey-50 font-medium text-md md:text-lg">
                            New AI Chatbot
                          </span>
                        </button>
                      </div>
                    )}
                    {chatbots.map((chatbot, index) => (
                      <ChatbotBlock
                        key={index}
                        icon={chatbot.icon || DANTE_ICON}
                        name={chatbot.name}
                        status={
                          chatbot.safety?.enabled
                            ? STATUS.ACTIVE
                            : STATUS.PAUSED
                        }
                        infos={chatbot.connection_stats}
                        insights={chatbot.insights}
                        stats={chatbot.stats || dataChart}
                        handleShareChatbot={() => {
                          handleShareChatbot({
                            kb_id: chatbot.kb_id,
                            llm_model:
                              chatbot.llm_model || LLM_MODEL_DEFAULT.value,
                          });
                        }}
                        openChatbot={() =>
                          navigate(`/chatbot/${chatbot.kb_id}`)
                        }
                      />
                    ))}
                  </div>
                </div>
              </div>
            </Transition>
          </>
        ) : (
          <div className="h-full flex items-center justify-center my-auto">
            <div className="bg-white rounded-size1 w-[300px] h-[300px] translate-y-[-15%]">
              <button
                className={'dbutton flex flex-col items-center justify-center rounded-size1 w-full h-full text-grey-20 text-lg md:text-xl '}
                onClick={() => {
                  if (checkEnv()) {
                    trackCreateChatbotClick({ user_id: user?.email });
                  }
                  resetChatbotData();
                  trackKlaviyoEvent('create_chatbot_clicked', {});
                  navigate('/chatbot/create');
                }}
              >
                <AddChatbotIcon className="size-10" />
                <span className="ml-2 mt-4 text-grey-50 font-medium text-md md:text-xs">
                  New AI Chatbot
                </span>
              </button>
            </div>
          </div>
        )}
        <DModalShareChatbot
          isOpen={showShareChatbot}
          kb_id={kbId}
          llm_model={llmModel}
          onClose={() => setShowShareChatbot(false)}
          origin="dashboard"
        />
      </div>
    </LayoutMain>
  );
};

export default ChatbotDashboard;
