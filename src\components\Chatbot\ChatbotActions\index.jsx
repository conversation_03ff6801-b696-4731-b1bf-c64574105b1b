import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import DButton from '@/components/Global/DButton';
import ShareChatbotIcon from '@/components/Global/Icons/ShareChatbotIcon';
import DModalShareChatbot from '../DModalShareChatbot';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import { LLM_MODEL_DEFAULT } from '@/constants';
import { deleteChatbot } from '@/services/chatbot.service';
import { getUserProfile } from '@/services/user.service';
import { checkTeamManagementPermission } from '@/helpers/tier/featureCheck';
import { useUserStore } from '@/stores/user/userStore';
import DNavLink from '@/components/Global/DNavLink';

const ChatbotActions = ({ 
  chatbotId, 
  selectedChatbot,
  className = '',
  variant = 'desktop' // 'desktop' or 'mobile'
}) => {
  const navigate = useNavigate();
  const setUser = useUserStore((state) => state.setUser);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const handleDeleteChatbot = async () => {
    try {
      setDeleteLoading(true);
      const response = await deleteChatbot(chatbotId);
      if (response.status === 200) {
        setShowDeleteModal(false);
        const profileResponse = await getUserProfile();
        if(profileResponse.status === 200){
          setUser(profileResponse.data);
        }
        navigate('/');
      }
    } catch (error) {
      console.error('Error deleting AI Chatbot:', error);
      setDeleteLoading(false);
    } finally {
      setDeleteLoading(false);
    }
  };

  if (variant === 'mobile') {
    return (
      <div className={`flex flex-col gap-size1 ${className}`}>
        <DButton
          className="!bg-purple-200 !text-white !h-[36px]"
          onClick={() => setShowShareModal(true)}
          fullWidth
        >
          <ShareChatbotIcon className="w-[14px] h-[14px]" />
          Share AI Chatbot
        </DButton>
        {checkTeamManagementPermission('delete_chatbot') && (
          <DButton
            variant="grey"
            onClick={() => setShowDeleteModal(true)}
            fullWidth
            size="md"
            className="xl:py-size0 2xl:py-size1 !text-base !h-[36px]"
          >
            Delete AI Chatbot
          </DButton>
        )}
        <RenderModals 
          showShareModal={showShareModal}
          showDeleteModal={showDeleteModal}
          setShowShareModal={setShowShareModal}
          setShowDeleteModal={setShowDeleteModal}
          handleDeleteChatbot={handleDeleteChatbot}
          deleteLoading={deleteLoading}
          chatbotId={chatbotId}
          selectedChatbot={selectedChatbot}
        />
      </div>
    );
  }

  return (
    <div className={`flex flex-col gap-size1 ${className}`}>
      <DNavLink
        label="Share AI Chatbot"
        iconPlacement="pre"
        icon={<ShareChatbotIcon className="w-[14px] h-[14px] !text-[#fff]" />}
        className="!bg-purple-200 !text-[#fff] !h-[36px]"
        onClick={() => setShowShareModal(true)}
      />
      {checkTeamManagementPermission('delete_chatbot') && (
        <DButton
          variant="grey"
          onClick={() => setShowDeleteModal(true)}
          fullWidth
          size="md"
          className="xl:py-size0 2xl:py-size1 !text-base !h-[36px]"
        >
          Delete AI Chatbot
        </DButton>
      )}
      <RenderModals 
        showShareModal={showShareModal}
        showDeleteModal={showDeleteModal}
        setShowShareModal={setShowShareModal}
        setShowDeleteModal={setShowDeleteModal}
        handleDeleteChatbot={handleDeleteChatbot}
        deleteLoading={deleteLoading}
        chatbotId={chatbotId}
        selectedChatbot={selectedChatbot}
      />
    </div>
  );
};

const RenderModals = ({
  showShareModal,
  showDeleteModal,
  setShowShareModal,
  setShowDeleteModal,
  handleDeleteChatbot,
  deleteLoading,
  chatbotId,
  selectedChatbot
}) => {
  return (
    <>
      <DModalShareChatbot
        kb_id={chatbotId}
        llm_model={selectedChatbot?.last_model_used?.value || LLM_MODEL_DEFAULT.value}
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
      />
      <DConfirmationModal
        open={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteChatbot}
        title="Delete a chatbot?"
        description="Are you sure you want to delete this chatbot? This action is irreversible."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={deleteLoading}
      />
    </>
  );
};

export default ChatbotActions; 