import { useState, useEffect } from 'react';
import { Link, useSearchParams, useNavigate } from 'react-router-dom';
import { useGoogleLogin } from '@react-oauth/google';
import signupGoogleUseCase from '@/application/auth/signupGoogle';
import { useUserStore } from '@/stores/user/userStore';
import DFullLogo from '@/components/Global/DLogo/DFullLogo';
import DLoading from '@/components/DLoading';

// Import our new components
import CreateAccount from './steps/CreateAccount';
import VerifyEmail from './steps/VerifyEmail';
import EnterName from './steps/EnterName';

// Define our signup steps
export const SignUpSteps = {
  CREATE_ACCOUNT: 'create_account',
  VERIFY_EMAIL: 'verify_email',
  ENTER_NAME: 'enter_name',
};

const SignUp = () => {
  const [urlParams] = useSearchParams();
  const navigate = useNavigate();
  const user = useUserStore((state) => state.user);
  const setUser = useUserStore((state) => state.setUser);

  // State for managing the flow
  const [currentStep, setCurrentStep] = useState(SignUpSteps.CREATE_ACCOUNT);
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [name, setName] = useState('');
  const [contactConsent, setContactConsent] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Page loading state
  const [pageLoading, setPageLoading] = useState(true);

  /**
   * Handles Google OAuth signup
   */
  const handleGoogleLogIn = useGoogleLogin({
    onSuccess: async (tokenResponse) => {
      setLoading(true);
      setError('');
      try {
        const rewardful_referral = window.Rewardful?.referral;
        const response = await signupGoogleUseCase({
          ...tokenResponse,
          rewardful_referral,
          contact_consent: contactConsent,
          isLanding: urlParams.get('is_landing_page'),
          kb_id: urlParams.get('kb_id'),
        });

        if (response.status === 200) {
          navigate('/first-time-setup');
        }
      } catch (err) {
        console.error(err);
        const errorMessage = err.response?.data?.detail || err.message || 'An error occurred during sign up';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    },
    onError: (error) => {
      console.error('Login Failed:', error);
      setError('Google sign up failed. Please try again.');
      setLoading(false);
    },
  });

  
  useEffect(() => {
    
    const handleLoad = () => {
      setTimeout(() => {
        setPageLoading(false);
      }, 500);
    };

    // If document is already loaded, call handleLoad directly
    if (document.readyState === 'complete') {
      handleLoad();
    } else {
      window.addEventListener('load', handleLoad);

      // Fallback: set pageLoading to false after a maximum timeout (2 seconds)
      const timeoutId = setTimeout(() => {
        setPageLoading(false);
      }, 2000);

      return () => {
        window.removeEventListener('load', handleLoad);
        clearTimeout(timeoutId);
      };
    }
  }, []);

  // Render the current step
  const renderStep = () => {
    switch (currentStep) {
      case SignUpSteps.CREATE_ACCOUNT:
        return (
          <CreateAccount
            email={email}
            setEmail={setEmail}
            contactConsent={contactConsent}
            setContactConsent={setContactConsent}
            handleGoogleLogIn={handleGoogleLogIn}
            goToNextStep={() => setCurrentStep(SignUpSteps.VERIFY_EMAIL)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
      case SignUpSteps.VERIFY_EMAIL:
        return (
          <VerifyEmail
            email={email}
            code={code}
            setCode={setCode}
            goToNextStep={() => navigate('/')}
            goBack={() => setCurrentStep(SignUpSteps.CREATE_ACCOUNT)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
            contactConsent={contactConsent}
          />
        );
      // case SignUpSteps.ENTER_NAME:
      //   return (
      //     <EnterName
      //       name={name}
      //       setName={setName}
      //       loading={loading}
      //       setLoading={setLoading}
      //       error={error}
      //       setError={setError}
      //     />
      //   );
      default:
        return (
          <CreateAccount
            email={email}
            setEmail={setEmail}
            contactConsent={contactConsent}
            setContactConsent={setContactConsent}
            handleGoogleLogIn={handleGoogleLogIn}
            goToNextStep={() => setCurrentStep(SignUpSteps.VERIFY_EMAIL)}
            loading={loading}
            setLoading={setLoading}
            error={error}
            setError={setError}
          />
        );
    }
  };

  if (pageLoading) {
    return <DLoading show={true} />;
  }

  return (
    <div className="relative flex flex-col items-center justify-center min-h-dvh w-full overflow-hidden">
      {/* <div
        className="absolute w-full h-full"
        style={{
          backgroundImage: 'radial-gradient(circle at center, rgb(196, 189, 247) 5%, rgb(189, 181, 252) 10%, white 45%)'
        }}
      ></div> */}

      <div className="absolute bottom-[-60%] right-[-20%] w-[120%] h-[120%] rounded-full bg-gradient-radial from-purple-200 to-transparent opacity-20"></div>

      {/* Content with entry animation */}
      <div className="relative z-10 w-full max-w-md p-4 animate-fadeIn">
        <div className="flex justify-center mb-4 h-8">
          <DFullLogo />
        </div>
        {/* Wrapper for both main content and login link with single animation */}
        <div className="animate-fadeInUp">
          {renderStep()}

          {/* Login Link - always shown */}
          <div className="bg-gray-100 py-4 rounded-b-3xl text-center shadow-lg mx-2 md:mx-0">
            <p className="text-sm text-gray-500">
              Already have an account?{' '}
              <Link to="/log-in" className="text-indigo-600 hover:underline">
                Log in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
