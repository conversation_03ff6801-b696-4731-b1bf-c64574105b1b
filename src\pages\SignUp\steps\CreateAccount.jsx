import { useState } from 'react';
import { Link } from 'react-router-dom';
import * as userService from '@/services/user.service';
import validateEmail from '@/helpers/validateEmail';

// SVG Icons as inline components
const GoogleIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.8 9.2C17.8 8.4 17.7 7.8 17.6 7.1H9.1V10.2H14C13.8 11.2 13.2 12 12.3 12.5V14.3H15.1C16.8 12.9 17.8 11.2 17.8 9.2Z" fill="#4285F4"/>
    <path d="M9.1 18C11.4 18 13.3 17.2 14.7 15.8L11.9 14C11.2 14.5 10.3 14.8 9.1 14.8C6.7 14.8 4.7 13.2 4 11H1.1V13C2.8 16.1 5.8 18 9.1 18Z" fill="#34A853"/>
    <path d="M4 11C3.8 10.4 3.7 9.7 3.7 9C3.7 8.3 3.8 7.6 4 7V5H1.1C0.4 6.2 0 7.6 0 9C0 10.4 0.4 11.8 1.1 13L4 11Z" fill="#FBBC05"/>
    <path d="M9.1 3.2C10.4 3.2 11.6 3.7 12.5 4.5L15 2C13.5 0.8 11.4 0 9.1 0C5.8 0 2.8 1.9 1.1 5L4 7C4.7 4.8 6.7 3.2 9.1 3.2Z" fill="#EA4335"/>
  </svg>
);

const AppleIcon = () => (
  <svg width="16" height="18" viewBox="0 0 16 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M12.6512 9.47411C12.6347 7.25389 14.5303 6.17008 14.6129 6.12092C13.4883 4.45389 11.6965 4.23103 11.1209 4.21308C9.54353 4.04758 8.02000 5.15933 7.22235 5.15933C6.40706 5.15933 5.17882 4.23103 3.86000 4.26695C2.16235 4.30286 0.590588 5.28581 -0.173647 6.79231C-1.75718 9.85667 -0.0401176 14.3747 1.46941 16.9337C2.47412 18.1847 3.65647 19.5933 5.19765 19.5394C6.69353 19.4854 7.28000 18.5928 9.06353 18.5928C10.83 18.5928 11.3835 19.5394 12.9476 19.5035C14.5653 19.4854 15.5841 18.2405 16.5529 16.9756C17.7065 15.525 18.1729 14.1104 18.1905 14.0384C18.1553 14.0205 15.2271 12.9626 15.1918 9.47411H12.6512Z" fill="black"/>
    <path d="M10.29 2.86344C11.0877 1.88852 11.64 0.543525 11.4753 -0.835938C10.3487 -0.781319 8.96459 -0.0681944 8.13165 0.871781C7.38753 1.73211 6.72941 3.13149 6.91176 4.45693C8.18459 4.54723 9.45747 3.82041 10.29 2.86344Z" fill="black"/>
  </svg>
);

const MicrosoftIcon = () => (
  <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8.5 8.5H0V0H8.5V8.5Z" fill="#F25022"/>
    <path d="M18 8.5H9.5V0H18V8.5Z" fill="#7FBA00"/>
    <path d="M8.5 18H0V9.5H8.5V18Z" fill="#00A4EF"/>
    <path d="M18 18H9.5V9.5H18V18Z" fill="#FFB900"/>
  </svg>
);

const EmailIcon = () => (
  <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M18 0H2C0.9 0 0.******** 0.9 0.******** 2L0 14C0 15.1 0.9 16 2 16H18C19.1 16 20 15.1 20 14V2C20 0.9 19.1 0 18 0ZM18 4L10 9L2 4V2L10 7L18 2V4Z" fill="white"/>
  </svg>
);

const CreateAccount = ({
  email,
  setEmail,
  contactConsent,
  setContactConsent,
  handleGoogleLogIn,
  goToNextStep,
  loading,
  setLoading,
  error,
  setError
}) => {
  const [emailError, setEmailError] = useState('');

  const validateForm = () => {
    let isValid = true;

    // Email validation
    if (!email || email.trim() === '') {
      setEmailError('Email is required');
      isValid = false;
    } else if (!validateEmail(email)) {
      setEmailError('Please enter a valid email address');
      isValid = false;
    } else if (email.includes('+')) {
      setEmailError('Emails with \'+\' are not allowed');
      isValid = false;
    } else {
      setEmailError('');
    }

    return isValid;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    setLoading(true);
    setError('');

    try {
      const response = await userService.sendEmailVerification(email);
      if (response.status === 200) {
        goToNextStep();
      }
    } catch (err) {
      console.error(err);
      setEmailError(err.response?.data?.detail || 'Error sending verification email');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialSignUp = (provider) => {
    if (provider === 'google') {
      handleGoogleLogIn();
    }
    // Apple and Microsoft implementations would go here
  };

  return (
    <div className="bg-white rounded-t-xl md:rounded-t-3xl shadow-lg p-4 md:p-7 mx-2 md:mx-0">
      {/* Header */}
      <div className="text-center mb-6 pt-2 animate-fadeInUpDelayed1">
        <h1 className="text-2xl font-medium mb-2">Create a free account</h1>
        <p className="text-gray-500 text-sm max-w-[280px] mx-auto">
          Join Dante AI, the fastest and easiest way to make AI chatbot agents.
        </p>
      </div>

      {/* Social Login Buttons */}
      <div className="space-y-3 mb-8 animate-fadeInUpDelayed2">
        <button
          onClick={() => handleSocialSignUp('google')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3"><GoogleIcon /></span>
          Sign up with Google
        </button>

        {/* <button
          onClick={() => handleSocialSignUp('apple')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3"><AppleIcon /></span>
          Sign up with Apple
        </button> */}

        {/* <button
          onClick={() => handleSocialSignUp('microsoft')}
          className="flex items-center justify-center w-full py-3 px-4 border border-gray-200 rounded-lg text-gray-700 font-medium text-base transition hover:bg-gray-50"
          disabled={loading}
        >
          <span className="mr-3"><MicrosoftIcon /></span>
          Sign up with Microsoft
        </button> */}
      </div>

      {/* Divider */}
      <div className="flex items-center mb-5">
        <div className="flex-grow border-t border-gray-200"></div>
        <span className="px-4 text-gray-400 text-sm font-medium">OR</span>
        <div className="flex-grow border-t border-gray-200"></div>
      </div>

      {/* Email Form */}
      <div className="mb-5 animate-fadeInUpDelayed3">
        <p className="text-sm text-gray-500 mb-3 text-center">
          We recommend using your work email
        </p>

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <input
              type="email"
              placeholder="<EMAIL>"
              className={`w-full py-3 px-4 border ${
                emailError ? 'border-red-500' : 'border-gray-200'
              } rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-200 text-gray-700 text-min-safe-input`}
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          <div className="mt-1 w-full px-2 max-w-sm">
               {(emailError !== '' || error !== '') && <p className="text-red-500 text-xs">{emailError || error}</p>}
            </div>
          </div>

          <button
            type="submit"
            className="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 hover:bg-indigo-700 transition"
            disabled={loading}
          >
            <EmailIcon />
            Sign up with work email
          </button>
          <div className="mt-4 flex items-start gap-2">
            <input
              type="checkbox"
              id="marketingConsent"
              className="mt-1 accent-indigo-600"
              checked={contactConsent}
              onChange={(e) => setContactConsent(e.target.checked)}
            />
            <label htmlFor="marketingConsent" className="text-xs text-gray-500">
              Send me additional free support and feature updates
            </label>
          </div>
{/* 
          <div className="h-6 mt-2">
            {error && <p className="text-red-500 text-sm text-center animate-fadeIn">{error}</p>}
          </div> */}
        </form>
      </div>

      {/* Terms */}
      <div className="text-center text-xs text-gray-500 mb-2">
        By proceeding, you agree to our{' '}
        <a href="https://www.dante-ai.com/terms-of-service" className="text-indigo-600 hover:underline">Terms</a> and{' '}
        <a href="https://www.dante-ai.com/privacy-policy" className="text-indigo-600 hover:underline">Privacy Policy</a>
      </div>


    </div>
  );
};

export default CreateAccount;
