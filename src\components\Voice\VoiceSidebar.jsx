import React, { useState } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import DNavLink from '../Global/DNavLink';
// import ConversationIcon from '../Global/Icons/ConversationIcon';
import AiChatbotIcon from '../Global/Icons/AiChatbotIcon';
import GraphIcon from '../Global/Icons/GraphIcon';
import useDanteApi from '@/hooks/useDanteApi';
import * as voiceService from '@/services/voice.service';
import DTooltip from '../Global/DTooltip';

export const VoiceSidebarStepEnum = {
  CONVERSATIONS: 1,
  ANALYTICS: 2,
};

const VoiceSidebar = () => {
  const { id } = useParams();
  const location = useLocation();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(VoiceSidebarStepEnum.CONVERSATIONS);
  const { data: voiceData } = useDante<PERSON>pi(voiceService.getVoice, [], {}, id);

  // Define voice nav items
  const voiceNavItems = [
    {
      id: VoiceSidebarStepEnum.CONVERSATIONS,
      label: 'Conversations',
      icon: <AiChatbotIcon className="w-[16px] h-[16px]" />,
      link: `/voice/${id}/conversations`,
    },
    {
      id: VoiceSidebarStepEnum.ANALYTICS,
      label: 'Analytics',
      icon: <GraphIcon className="w-[16px] h-[16px]" />,
      link: `/voice/${id}/analytics`,
    },
  ];

  return (
    <div className="voice_nav_wrapper flex flex-col gap-size5 w-full md:w-[184px]">
      <DTooltip content={voiceData?.name}>
        <span className="hidden md:block text-xl font-medium tracking-tight truncate w-[184px]">
          {voiceData?.name}
        </span>
      </DTooltip>
      <div className="hidden md:block w-full h-px bg-grey-5"></div>
      <div className="flex md:flex-col gap-size1 overflow-y-auto no-scrollbar ">
        {voiceNavItems.map((navItem) => {
          const isActive = location.pathname === navItem.link || 
                          (navItem.id === VoiceSidebarStepEnum.CONVERSATIONS && 
                           location.pathname.includes(`/voice/${id}/conversations/`));

          return (
            <DNavLink
              key={navItem.label}
              label={navItem.label}
              icon={navItem.icon}
              iconPlacement="pre"
              active={isActive}
              onClick={() => {
                setCurrentStep(navItem.id);
                navigate(navItem.link);
              }}
            />
          );
        })}
      </div>
    </div>
  );
};

export default VoiceSidebar; 