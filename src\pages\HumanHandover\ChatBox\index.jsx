import ChatManager from '@/components/Chatbot/ChatManager';
import CategoryIcon from '@/components/Global/CategoryIcon';
import DButtonIcon from '@/components/Global/DButtonIcon';
import ChevronDownIcon from '@/components/Global/Icons/ChevronDownIcon';
import ChevronRightIcon from '@/components/Global/Icons/ChevronRightIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import CopyIcon from '@/components/Global/Icons/CopyIcon';
import FlagIcon from '@/components/Global/Icons/FlagIcon';
import React, { useEffect, useState } from 'react';
import useDanteApi from '@/hooks/useDanteApi';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import ChatConversation from '../ChatConversation';

const ChatBox = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { openedConversation, addOpenedConversation, setCloseConversation } =
    useHumanHandoverStore((state) => state);

  useEffect(() => {
    if (openedConversation) {
      const conversationOpen = openedConversation.find((c) => c.open === true);
      if (conversationOpen) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    }
  }, [openedConversation]);

  const handleBackdropClick = () => {
    // Close all open conversations
    openedConversation?.forEach(conversation => {
      if (conversation.open) {
        addOpenedConversation({ ...conversation, open: false });
      }
    });
  };

  return (
    <>
      <div
        className={`${
          isOpen ? 'block' : 'hidden'
        } fixed bottom-0 right-0 left-0 top-0 bg-[#000]/30 z-10`}
        onClick={handleBackdropClick}
      ></div>
      <div className="flex  gap-size3 fixed bottom-0 right-6 z-20 rounded-t-size1">
        {openedConversation?.map((conversation) => (
          <div
            key={conversation.id}
            className={`rounded-t-size1${
              conversation.open
                ? 'w-[464px] h-[624px]'
                : 'w-[300px] flex flex-col justify-end'
            }`}
          >
            {!conversation.open && (
              <button
                className="bg-white text-black py-size3 px-size2 flex justify-between items-center border border-grey-10 rounded-t-size1 w-full"
                onClick={() => {
                  addOpenedConversation({ ...conversation, open: true });
                }}
              >
                <div className="flex gap-size1">
                  <p className="text-lg font-regular tracking-tight">
                    Conversation #
                    {conversation.request_id.toString().padStart(4, '0')}
                  </p>
                  <div className="flex items-center justify-center border border-orange-300 rounded-full size-6">
                    <FlagIcon className="text-orange-300 size-3" />
                  </div>
                </div>
                <ChevronDownIcon className="size-4" />
              </button>
            )}

            <ChatConversation
              key={conversation.id}
              conversation={conversation}
              mode="box"
            />
          </div>
        ))}
      </div>
    </>
  );
};

export default ChatBox;
