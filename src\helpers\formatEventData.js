const formatEventData = (data) => {
  // <PERSON> already gives you a string in `data` for each SSE chunk.
  // Only stringify if someone passes an object by mistake:
  let str = typeof data === 'string' ? data : JSON.stringify(data);

  // 1. Restore newlines & quote placeholders
  str = str
    .replaceAll('_DANTE_NEW_LINE_', '\n')
    .replaceAll('_DANTE_DOUBLE_QUOTE_', '"')
    .replaceAll('_DANTE_SINGLE_QUOTE_', "'");

  // 2. LEAVE every other character as-is:
  //    – don’t delete "
  //    – don’t wrap text between /…/ in <b>

  return str;
};

export default formatEventData;
