import React, { useState, useEffect, useRef } from 'react';
import WaveSurfer from 'wavesurfer.js';
import RecordPlugin from 'wavesurfer.js/dist/plugins/record.js';
import DButtonIcon from '../Global/DButtonIcon';
import MicrophoneIcon from '@/components/Global/Icons/MicrophoneIcon';
import StopIcon from '../Global/Icons/StopIcon';
import clsx from 'clsx';

const AudioRecorder = ({
  handleRecordingComplete,
  setQuestion,
  isRecording,
  setIsRecording,
  isPreviewMode,
}) => {
  const wavesurferRef = useRef(null);
  const recognitionRef = useRef(null);
  const waveformRef = useRef(null);
  const recordPluginRef = useRef(null);
  const [transcribedText, setTranscribedText] = useState('');
  const [error, setError] = useState(null);

  useEffect(() => {
    let wavesurfer = null;
    let record = null;

    const initializeWaveSurfer = async () => {
      if (isRecording && waveformRef.current && !wavesurferRef.current) {
        try {
          // First check for microphone permissions
          const stream = await navigator.mediaDevices.getUserMedia({ 
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true
            } 
          });

          // Create WaveSurfer instance
          wavesurfer = WaveSurfer.create({
            container: waveformRef.current,
            waveColor: '#000',
            progressColor: '#09081F33',
            height: 20,
            cursorWidth: 0,
            barWidth: 2,
            barGap: 2,
            responsive: true,
            interact: false,
          });

          // Initialize record plugin with the stream
          record = RecordPlugin.create({
            scrollingWaveform: true,
            mediaStream: stream
          });

          wavesurferRef.current = wavesurfer;
          recordPluginRef.current = record;
          wavesurferRef.current.registerPlugin(record);

          // Start recording
          await recordPluginRef.current.startRecording();
          const recognition = initializeSpeechRecognition();
          if (recognition) {
            recognitionRef.current = recognition;
            recognition.onend = () => {
              if (isRecording) {
                recognition.start();
              }
            };
            recognition.start();
          }
        } catch (error) {
          console.error('Error initializing WaveSurfer:', error);
          let errorMessage = 'Error accessing the microphone';
          
          if (error.name === 'NotAllowedError') {
            errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
          } else if (error.name === 'NotFoundError') {
            errorMessage = 'No microphone found. Please connect a microphone and try again.';
          } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
            errorMessage = 'Microphone is in use by another application.';
          }
          
          setError(errorMessage);
          setIsRecording(false);
        }
      }
    };

    // Initialize with a small delay to ensure DOM is ready
    const timeoutId = setTimeout(initializeWaveSurfer, 100);

    return () => {
      clearTimeout(timeoutId);
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
      if (recordPluginRef.current) {
        recordPluginRef.current = null;
      }
    };
  }, [isRecording]);

  const initializeSpeechRecognition = () => {
    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    if (!SpeechRecognition) {
      console.error('Speech recognition not supported');
      return null;
    }
  
    const recognition = new SpeechRecognition();
    recognition.lang = 'en-US';
    recognition.interimResults = true;
    recognition.continuous = true;
  
    recognition.onresult = (event) => {
      let finalTranscript = '';
      for (let i = event.resultIndex; i < event.results.length; i++) {
        if (event.results[i].isFinal) {
          finalTranscript += event.results[i][0].transcript;
        }
      }
      setTranscribedText(prev => prev + finalTranscript);
      setQuestion(prev => prev + finalTranscript);
    };
  
    recognition.onerror = (event) => {
      console.error('Speech recognition error:', event.error);
      setError('Speech recognition error. Please try again.');
      setIsRecording(false);
    };
  
    return recognition;
  };

  const startRecording = async () => {
    if (isPreviewMode) return;
    setError(null);
    try {
      // Check microphone permission first
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setIsRecording(true);
      setTranscribedText('');
    } catch (error) {
      console.error('Error accessing microphone:', error);
      let errorMessage = 'Error accessing the microphone';
      
      if (error.name === 'NotAllowedError') {
        errorMessage = 'Microphone access denied. Please allow microphone access in your browser settings.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = 'No microphone found. Please connect a microphone and try again.';
      } else if (error.name === 'NotReadableError' || error.name === 'TrackStartError') {
        errorMessage = 'Microphone is in use by another application.';
      }
      
      setError(errorMessage);
    }
  };
  
  const stopRecording = async () => {
    try {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy();
        wavesurferRef.current = null;
      }
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
      if (recordPluginRef.current) {
        recordPluginRef.current = null;
      }

      setIsRecording(false);
      setError(null);
      handleRecordingComplete(false);
    } catch (error) {
      console.error('Error stopping recording:', error);
      setError('Error stopping recording. Please try again.');
    }
  };

  return (
    !isRecording ? (
      <div className="flex items-center gap-size2 w-full h-full px-1 border border-[rgba(9, 8, 31,0.1)] rounded-full">
        <DButtonIcon
          className={clsx('audio-recorder-button !rounded-full bg-grey-5 hover:bg-[rgba(9, 8, 31,0.05)]', {
            'cursor-default': isPreviewMode,
          })}
          onClick={startRecording}
        >
          <MicrophoneIcon className="w-4 h-4 text-[rgba(9, 8, 31,0.5)]"/>
        </DButtonIcon>
        <span className="text-[rgba(9, 8, 31,0.5)] text-sm">
          {error || 'Start recording'}
        </span>
      </div>
    ) : (
      <div className="flex flex-col w-full h-[80px] absolute left-0 -top-[50px] bg-white rounded-2xl border border-b-0 border-[rgba(9, 8, 31,0.1)]">
        <div className="px-3 py-2 min-h-[24px] text-[rgba(9, 8, 31,0.75)] text-sm">
          {error ? (
            <span className="text-red-500">{error}</span>
          ) : (
            transcribedText || <span className="text-[rgba(9, 8, 31,0.5)]">Listening...</span>
          )}
        </div>
        <div className="flex items-end gap-size2 w-full px-3 py-2 border-y rounded-b-2xl border-[rgba(9, 8, 31,0.1)]">
          <div ref={waveformRef} className="grow"></div>
          <DButtonIcon
            className="audio-recorder-button-stop !rounded-full !bg-grey-5"
            onClick={stopRecording}
          >
            <StopIcon className="w-5 h-5 text-[rgba(9, 8, 31,0.5)]" />
          </DButtonIcon>
        </div>
      </div>
    )
  );
};

export default AudioRecorder;