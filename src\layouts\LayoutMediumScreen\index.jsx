import React, { useMemo, memo, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DNotificationHeader from '@/components/DNotificationHeader';
import DButton from '@/components/Global/DButton';
import DButtonIcon from '@/components/Global/DButtonIcon';
import DProgressBar from '@/components/Global/DProgressBar';
import BookIcon from '@/components/Global/Icons/BookIcon';
import ZapIcon from '@/components/Global/Icons/ZapIcon';
import HeaderAccountMenu from '@/components/HeaderAccountMenu';
import MainNav from '@/components/MainNav';
import HeaderLearningHub from '../../components/HeaderLearningHub';
import DToastContainer from '@/components/DToast/DToastContainer';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import UserGroupIcon from '@/components/Global/Icons/UserGroupIcon';
import UserPlusIcon from '@/components/Global/Icons/UserPlusIcon';
import InviteTeamMember from '@/components/InviteTeamMember';
import ReferralProgram from '@/components/ReferralProgram';
import { useUserStore } from '@/stores/user/userStore';
import UpgradePlanIcon from '@/components/Global/Icons/UpgradePlanIcon';
import DModalPlans from '@/components/DModalPlans';
import useLayoutStore from '@/stores/layout/layoutStore';
import AiChatbotIcon from '@/components/Global/Icons/AiChatbotIcon';
import SlidingChatSidebar from '@/components/Global/SlidingChatSidebar';
import AnimatedChatButton from '@/components/Global/AnimatedChatButton';
import ChevronDownIcon from '@/components/Global/Icons/ChevronDownIcon';

const LayoutMediumScreen = ({ children, title, progressBar }) => {
  const selectedTeam = useTeamManagementStore((state) => state.selectedTeam);
  const user = useUserStore((state) => state.user);
  const navigate = useNavigate();
  const userData = useUserStore((state) => state.user);
  const { planModal, setPlanModal } = useLayoutStore((state) => state);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [isChatMinimized, setIsChatMinimized] = useState(false);

  useEffect(() => {
    setPlanModal({
      show: window.location.pathname !== '/plans' && !selectedTeam,
    });
  }, [selectedTeam, setPlanModal, window.location.pathname]);

  const addPlanButton = useMemo(() => {
    if (!planModal.show) return null;


    const isProYearly = user?.tier_type === 'pro' && user?.tier_key?.period === 'yearly';
    const buttonText = isProYearly ? 'Membership' : (userData?.new_design_trial ? 'Add plan' : 'Membership');

    return (
      <DButton
        className="!gap-size0 bg-white"
        onClick={() =>
          setPlanModal({
            isOpen: true,
          })
        }
      >
        <UpgradePlanIcon className="mb-[3.5px]" />
        <span>{buttonText}</span>
      </DButton>
    );
  }, [planModal.show, userData?.new_design_trial, user?.tier_type, user?.tier_key?.period]);

  return (
    <>
      <main className={`layout-medium-screen d-h-screen flex flex-row gap-size2 xl:gap-size5 w-full p-size3 transition-all duration-500 ${isChatOpen && !isChatMinimized ? 'pr-[420px]' : ''}`}>
        <MainNav />

        <div className="w-full flex flex-col h-full gap-size2">
          <header className="flex justify-between pb-[15px] pt-size1">
            <div className="flex gap-size1 w-full md:max-w-[70%] items-center py-size1">
              {title && <h1 className="text-[22px] w-[300px] h-[14px]">{title}</h1>}
              {progressBar && progressBar.length > 0 && (
                <DProgressBar steps={progressBar} />
              )}
            </div>

            <DToastContainer hideInMobile />

            <div className="flex items-center justify-end">
              <div className="mx-2">
                <AnimatedChatButton 
                  onClick={() => {
                    setIsChatOpen(!isChatOpen);
                    setIsChatMinimized(false);
                  }}
                  isOpen={isChatOpen}
                />
              </div>
              <div className="flex gap-size2 items-center mr-[10px]">
                {addPlanButton}
                {user?.id && <InviteTeamMember />}
              </div>

              <div className="flex mr-[2px]">
                <DNotificationHeader />
                <HeaderLearningHub />
              </div>
              <div className="flex ml-[10px]">
                <HeaderAccountMenu />
              </div>
            </div>
          </header>
          <div className="h-[1px] flex flex-col grow">{children}</div>
        </div>
      </main>

      {isChatOpen && !isChatMinimized && (
        <SlidingChatSidebar
          isOpen={isChatOpen}
          onClose={() => setIsChatOpen(false)}
          onMinimize={() => setIsChatMinimized(true)}
        >
          <div className="h-full w-full">
            <iframe src="https://app.dante-ai.com/embed/?kb_id=ca7ac107-9716-4ef3-a6b5-55ceab49f15a&token=5e5ff150-8321-43cc-b0ed-5f89b44ecf81&modeltype=gpt-4-omnimodel-mini&tabs=false" allow="clipboard-write; clipboard-read; ;microphone" width="100%" height="100%" frameBorder="0"></iframe>
          </div>
        </SlidingChatSidebar>
      )}

      {isChatOpen && isChatMinimized && (
        <div className="fixed bottom-6 right-6 z-50 bg-white shadow-lg rounded-full flex items-center px-4 py-2 border border-grey-10 cursor-pointer min-w-[120px] min-h-[48px]" onClick={() => setIsChatMinimized(false)}>
          <AiChatbotIcon className="w-4 h-4 text-purple-300 mr-2 mt-0.5" />
          <span className="font-medium text-grey-75 text-sm">Chat with us</span>
        </div>
      )}

      <DModalPlans
        isOpen={planModal.isOpen}
        onClose={() => setPlanModal({ isOpen: false })}
      />
    </>
  );
};

// Use memo to prevent unnecessary re-renders
export default memo(LayoutMediumScreen);
